function Lh(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const o=Object.getOwnPropertyDescriptor(r,i);o&&Object.defineProperty(e,i,o.get?o:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var Ve=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function dl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function $h(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}),n}var yd={exports:{}},Uo={},wd={exports:{}},N={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pi=Symbol.for("react.element"),Nh=Symbol.for("react.portal"),zh=Symbol.for("react.fragment"),Uh=Symbol.for("react.strict_mode"),Fh=Symbol.for("react.profiler"),Mh=Symbol.for("react.provider"),Bh=Symbol.for("react.context"),Wh=Symbol.for("react.forward_ref"),qh=Symbol.for("react.suspense"),Vh=Symbol.for("react.memo"),Hh=Symbol.for("react.lazy"),Eu=Symbol.iterator;function Kh(e){return e===null||typeof e!="object"?null:(e=Eu&&e[Eu]||e["@@iterator"],typeof e=="function"?e:null)}var xd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_d=Object.assign,kd={};function lr(e,t,n){this.props=e,this.context=t,this.refs=kd,this.updater=n||xd}lr.prototype.isReactComponent={};lr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};lr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Sd(){}Sd.prototype=lr.prototype;function pl(e,t,n){this.props=e,this.context=t,this.refs=kd,this.updater=n||xd}var fl=pl.prototype=new Sd;fl.constructor=pl;_d(fl,lr.prototype);fl.isPureReactComponent=!0;var bu=Array.isArray,Ed=Object.prototype.hasOwnProperty,hl={current:null},bd={key:!0,ref:!0,__self:!0,__source:!0};function jd(e,t,n){var r,i={},o=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(o=""+t.key),t)Ed.call(t,r)&&!bd.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(s===1)i.children=n;else if(1<s){for(var l=Array(s),u=0;u<s;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)i[r]===void 0&&(i[r]=s[r]);return{$$typeof:pi,type:e,key:o,ref:a,props:i,_owner:hl.current}}function Jh(e,t){return{$$typeof:pi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ml(e){return typeof e=="object"&&e!==null&&e.$$typeof===pi}function Qh(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ju=/\/+/g;function xa(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Qh(""+e.key):t.toString(36)}function Qi(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(o){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case pi:case Nh:a=!0}}if(a)return a=e,i=i(a),e=r===""?"."+xa(a,0):r,bu(i)?(n="",e!=null&&(n=e.replace(ju,"$&/")+"/"),Qi(i,t,n,"",function(u){return u})):i!=null&&(ml(i)&&(i=Jh(i,n+(!i.key||a&&a.key===i.key?"":(""+i.key).replace(ju,"$&/")+"/")+e)),t.push(i)),1;if(a=0,r=r===""?".":r+":",bu(e))for(var s=0;s<e.length;s++){o=e[s];var l=r+xa(o,s);a+=Qi(o,t,n,l,i)}else if(l=Kh(e),typeof l=="function")for(e=l.call(e),s=0;!(o=e.next()).done;)o=o.value,l=r+xa(o,s++),a+=Qi(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function bi(e,t,n){if(e==null)return e;var r=[],i=0;return Qi(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Gh(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ke={current:null},Gi={transition:null},Xh={ReactCurrentDispatcher:ke,ReactCurrentBatchConfig:Gi,ReactCurrentOwner:hl};function Cd(){throw Error("act(...) is not supported in production builds of React.")}N.Children={map:bi,forEach:function(e,t,n){bi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return bi(e,function(){t++}),t},toArray:function(e){return bi(e,function(t){return t})||[]},only:function(e){if(!ml(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};N.Component=lr;N.Fragment=zh;N.Profiler=Fh;N.PureComponent=pl;N.StrictMode=Uh;N.Suspense=qh;N.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Xh;N.act=Cd;N.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=_d({},e.props),i=e.key,o=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,a=hl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(l in t)Ed.call(t,l)&&!bd.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&s!==void 0?s[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){s=Array(l);for(var u=0;u<l;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:pi,type:e.type,key:i,ref:o,props:r,_owner:a}};N.createContext=function(e){return e={$$typeof:Bh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Mh,_context:e},e.Consumer=e};N.createElement=jd;N.createFactory=function(e){var t=jd.bind(null,e);return t.type=e,t};N.createRef=function(){return{current:null}};N.forwardRef=function(e){return{$$typeof:Wh,render:e}};N.isValidElement=ml;N.lazy=function(e){return{$$typeof:Hh,_payload:{_status:-1,_result:e},_init:Gh}};N.memo=function(e,t){return{$$typeof:Vh,type:e,compare:t===void 0?null:t}};N.startTransition=function(e){var t=Gi.transition;Gi.transition={};try{e()}finally{Gi.transition=t}};N.unstable_act=Cd;N.useCallback=function(e,t){return ke.current.useCallback(e,t)};N.useContext=function(e){return ke.current.useContext(e)};N.useDebugValue=function(){};N.useDeferredValue=function(e){return ke.current.useDeferredValue(e)};N.useEffect=function(e,t){return ke.current.useEffect(e,t)};N.useId=function(){return ke.current.useId()};N.useImperativeHandle=function(e,t,n){return ke.current.useImperativeHandle(e,t,n)};N.useInsertionEffect=function(e,t){return ke.current.useInsertionEffect(e,t)};N.useLayoutEffect=function(e,t){return ke.current.useLayoutEffect(e,t)};N.useMemo=function(e,t){return ke.current.useMemo(e,t)};N.useReducer=function(e,t,n){return ke.current.useReducer(e,t,n)};N.useRef=function(e){return ke.current.useRef(e)};N.useState=function(e){return ke.current.useState(e)};N.useSyncExternalStore=function(e,t,n){return ke.current.useSyncExternalStore(e,t,n)};N.useTransition=function(){return ke.current.useTransition()};N.version="18.3.1";wd.exports=N;var k=wd.exports;const Fo=dl(k),Yh=Lh({__proto__:null,default:Fo},[k]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zh=k,em=Symbol.for("react.element"),tm=Symbol.for("react.fragment"),nm=Object.prototype.hasOwnProperty,rm=Zh.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,im={key:!0,ref:!0,__self:!0,__source:!0};function Pd(e,t,n){var r,i={},o=null,a=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)nm.call(t,r)&&!im.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:em,type:e,key:o,ref:a,props:i,_owner:rm.current}}Uo.Fragment=tm;Uo.jsx=Pd;Uo.jsxs=Pd;yd.exports=Uo;var x=yd.exports,ns={},Td={exports:{}},Ue={},Od={exports:{}},Rd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,R){var L=P.length;P.push(R);e:for(;0<L;){var H=L-1>>>1,J=P[H];if(0<i(J,R))P[H]=R,P[L]=J,L=H;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var R=P[0],L=P.pop();if(L!==R){P[0]=L;e:for(var H=0,J=P.length,Yt=J>>>1;H<Yt;){var je=2*(H+1)-1,Sn=P[je],we=je+1,Zt=P[we];if(0>i(Sn,L))we<J&&0>i(Zt,Sn)?(P[H]=Zt,P[we]=L,H=we):(P[H]=Sn,P[je]=L,H=je);else if(we<J&&0>i(Zt,L))P[H]=Zt,P[we]=L,H=we;else break e}}return R}function i(P,R){var L=P.sortIndex-R.sortIndex;return L!==0?L:P.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var a=Date,s=a.now();e.unstable_now=function(){return a.now()-s}}var l=[],u=[],c=1,d=null,p=3,v=!1,g=!1,w=!1,_=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(P){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=P)r(u),R.sortIndex=R.expirationTime,t(l,R);else break;R=n(u)}}function y(P){if(w=!1,f(P),!g)if(n(l)!==null)g=!0,vr(S);else{var R=n(u);R!==null&&kn(y,R.startTime-P)}}function S(P,R){g=!1,w&&(w=!1,m(O),O=-1),v=!0;var L=p;try{for(f(R),d=n(l);d!==null&&(!(d.expirationTime>R)||P&&!Ee());){var H=d.callback;if(typeof H=="function"){d.callback=null,p=d.priorityLevel;var J=H(d.expirationTime<=R);R=e.unstable_now(),typeof J=="function"?d.callback=J:d===n(l)&&r(l),f(R)}else r(l);d=n(l)}if(d!==null)var Yt=!0;else{var je=n(u);je!==null&&kn(y,je.startTime-R),Yt=!1}return Yt}finally{d=null,p=L,v=!1}}var b=!1,j=null,O=-1,F=5,D=-1;function Ee(){return!(e.unstable_now()-D<F)}function ct(){if(j!==null){var P=e.unstable_now();D=P;var R=!0;try{R=j(!0,P)}finally{R?rt():(b=!1,j=null)}}else b=!1}var rt;if(typeof h=="function")rt=function(){h(ct)};else if(typeof MessageChannel<"u"){var ae=new MessageChannel,be=ae.port2;ae.port1.onmessage=ct,rt=function(){be.postMessage(null)}}else rt=function(){_(ct,0)};function vr(P){j=P,b||(b=!0,rt())}function kn(P,R){O=_(function(){P(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){g||v||(g=!0,vr(S))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(P){switch(p){case 1:case 2:case 3:var R=3;break;default:R=p}var L=p;p=R;try{return P()}finally{p=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,R){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var L=p;p=P;try{return R()}finally{p=L}},e.unstable_scheduleCallback=function(P,R,L){var H=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?H+L:H):L=H,P){case 1:var J=-1;break;case 2:J=250;break;case 5:J=**********;break;case 4:J=1e4;break;default:J=5e3}return J=L+J,P={id:c++,callback:R,priorityLevel:P,startTime:L,expirationTime:J,sortIndex:-1},L>H?(P.sortIndex=L,t(u,P),n(l)===null&&P===n(u)&&(w?(m(O),O=-1):w=!0,kn(y,L-H))):(P.sortIndex=J,t(l,P),g||v||(g=!0,vr(S))),P},e.unstable_shouldYield=Ee,e.unstable_wrapCallback=function(P){var R=p;return function(){var L=p;p=R;try{return P.apply(this,arguments)}finally{p=L}}}})(Rd);Od.exports=Rd;var om=Od.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var am=k,ze=om;function E(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Id=new Set,Vr={};function yn(e,t){Yn(e,t),Yn(e+"Capture",t)}function Yn(e,t){for(Vr[e]=t,e=0;e<t.length;e++)Id.add(t[e])}var yt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),rs=Object.prototype.hasOwnProperty,sm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Cu={},Pu={};function lm(e){return rs.call(Pu,e)?!0:rs.call(Cu,e)?!1:sm.test(e)?Pu[e]=!0:(Cu[e]=!0,!1)}function um(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function cm(e,t,n,r){if(t===null||typeof t>"u"||um(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Se(e,t,n,r,i,o,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new Se(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new Se(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new Se(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new Se(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new Se(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new Se(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new Se(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new Se(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new Se(e,5,!1,e.toLowerCase(),null,!1,!1)});var vl=/[\-:]([a-z])/g;function gl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(vl,gl);fe[t]=new Se(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(vl,gl);fe[t]=new Se(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(vl,gl);fe[t]=new Se(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new Se(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new Se("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new Se(e,1,!1,e.toLowerCase(),null,!0,!0)});function yl(e,t,n,r){var i=fe.hasOwnProperty(t)?fe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(cm(t,n,i,r)&&(n=null),r||i===null?lm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var kt=am.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ji=Symbol.for("react.element"),Dn=Symbol.for("react.portal"),Ln=Symbol.for("react.fragment"),wl=Symbol.for("react.strict_mode"),is=Symbol.for("react.profiler"),Ad=Symbol.for("react.provider"),Dd=Symbol.for("react.context"),xl=Symbol.for("react.forward_ref"),os=Symbol.for("react.suspense"),as=Symbol.for("react.suspense_list"),_l=Symbol.for("react.memo"),bt=Symbol.for("react.lazy"),Ld=Symbol.for("react.offscreen"),Tu=Symbol.iterator;function yr(e){return e===null||typeof e!="object"?null:(e=Tu&&e[Tu]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,_a;function Cr(e){if(_a===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);_a=t&&t[1]||""}return`
`+_a+e}var ka=!1;function Sa(e,t){if(!e||ka)return"";ka=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),a=i.length-1,s=o.length-1;1<=a&&0<=s&&i[a]!==o[s];)s--;for(;1<=a&&0<=s;a--,s--)if(i[a]!==o[s]){if(a!==1||s!==1)do if(a--,s--,0>s||i[a]!==o[s]){var l=`
`+i[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=a&&0<=s);break}}}finally{ka=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Cr(e):""}function dm(e){switch(e.tag){case 5:return Cr(e.type);case 16:return Cr("Lazy");case 13:return Cr("Suspense");case 19:return Cr("SuspenseList");case 0:case 2:case 15:return e=Sa(e.type,!1),e;case 11:return e=Sa(e.type.render,!1),e;case 1:return e=Sa(e.type,!0),e;default:return""}}function ss(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ln:return"Fragment";case Dn:return"Portal";case is:return"Profiler";case wl:return"StrictMode";case os:return"Suspense";case as:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Dd:return(e.displayName||"Context")+".Consumer";case Ad:return(e._context.displayName||"Context")+".Provider";case xl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case _l:return t=e.displayName||null,t!==null?t:ss(e.type)||"Memo";case bt:t=e._payload,e=e._init;try{return ss(e(t))}catch{}}return null}function pm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ss(t);case 8:return t===wl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function qt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function $d(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function fm(e){var t=$d(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(a){r=""+a,o.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ci(e){e._valueTracker||(e._valueTracker=fm(e))}function Nd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$d(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function so(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ls(e,t){var n=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ou(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=qt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function zd(e,t){t=t.checked,t!=null&&yl(e,"checked",t,!1)}function us(e,t){zd(e,t);var n=qt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?cs(e,t.type,n):t.hasOwnProperty("defaultValue")&&cs(e,t.type,qt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ru(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function cs(e,t,n){(t!=="number"||so(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Pr=Array.isArray;function Hn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+qt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function ds(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(E(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Iu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(E(92));if(Pr(n)){if(1<n.length)throw Error(E(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:qt(n)}}function Ud(e,t){var n=qt(t.value),r=qt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Au(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Fd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ps(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Fd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Pi,Md=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Pi=Pi||document.createElement("div"),Pi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Pi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Hr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ir={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hm=["Webkit","ms","Moz","O"];Object.keys(Ir).forEach(function(e){hm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ir[t]=Ir[e]})});function Bd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ir.hasOwnProperty(e)&&Ir[e]?(""+t).trim():t+"px"}function Wd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Bd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var mm=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function fs(e,t){if(t){if(mm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(E(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(E(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(E(61))}if(t.style!=null&&typeof t.style!="object")throw Error(E(62))}}function hs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ms=null;function kl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var vs=null,Kn=null,Jn=null;function Du(e){if(e=mi(e)){if(typeof vs!="function")throw Error(E(280));var t=e.stateNode;t&&(t=Vo(t),vs(e.stateNode,e.type,t))}}function qd(e){Kn?Jn?Jn.push(e):Jn=[e]:Kn=e}function Vd(){if(Kn){var e=Kn,t=Jn;if(Jn=Kn=null,Du(e),t)for(e=0;e<t.length;e++)Du(t[e])}}function Hd(e,t){return e(t)}function Kd(){}var Ea=!1;function Jd(e,t,n){if(Ea)return e(t,n);Ea=!0;try{return Hd(e,t,n)}finally{Ea=!1,(Kn!==null||Jn!==null)&&(Kd(),Vd())}}function Kr(e,t){var n=e.stateNode;if(n===null)return null;var r=Vo(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(E(231,t,typeof n));return n}var gs=!1;if(yt)try{var wr={};Object.defineProperty(wr,"passive",{get:function(){gs=!0}}),window.addEventListener("test",wr,wr),window.removeEventListener("test",wr,wr)}catch{gs=!1}function vm(e,t,n,r,i,o,a,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Ar=!1,lo=null,uo=!1,ys=null,gm={onError:function(e){Ar=!0,lo=e}};function ym(e,t,n,r,i,o,a,s,l){Ar=!1,lo=null,vm.apply(gm,arguments)}function wm(e,t,n,r,i,o,a,s,l){if(ym.apply(this,arguments),Ar){if(Ar){var u=lo;Ar=!1,lo=null}else throw Error(E(198));uo||(uo=!0,ys=u)}}function wn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Qd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Lu(e){if(wn(e)!==e)throw Error(E(188))}function xm(e){var t=e.alternate;if(!t){if(t=wn(e),t===null)throw Error(E(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Lu(i),e;if(o===r)return Lu(i),t;o=o.sibling}throw Error(E(188))}if(n.return!==r.return)n=i,r=o;else{for(var a=!1,s=i.child;s;){if(s===n){a=!0,n=i,r=o;break}if(s===r){a=!0,r=i,n=o;break}s=s.sibling}if(!a){for(s=o.child;s;){if(s===n){a=!0,n=o,r=i;break}if(s===r){a=!0,r=o,n=i;break}s=s.sibling}if(!a)throw Error(E(189))}}if(n.alternate!==r)throw Error(E(190))}if(n.tag!==3)throw Error(E(188));return n.stateNode.current===n?e:t}function Gd(e){return e=xm(e),e!==null?Xd(e):null}function Xd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Xd(e);if(t!==null)return t;e=e.sibling}return null}var Yd=ze.unstable_scheduleCallback,$u=ze.unstable_cancelCallback,_m=ze.unstable_shouldYield,km=ze.unstable_requestPaint,ee=ze.unstable_now,Sm=ze.unstable_getCurrentPriorityLevel,Sl=ze.unstable_ImmediatePriority,Zd=ze.unstable_UserBlockingPriority,co=ze.unstable_NormalPriority,Em=ze.unstable_LowPriority,ep=ze.unstable_IdlePriority,Mo=null,lt=null;function bm(e){if(lt&&typeof lt.onCommitFiberRoot=="function")try{lt.onCommitFiberRoot(Mo,e,void 0,(e.current.flags&128)===128)}catch{}}var et=Math.clz32?Math.clz32:Pm,jm=Math.log,Cm=Math.LN2;function Pm(e){return e>>>=0,e===0?32:31-(jm(e)/Cm|0)|0}var Ti=64,Oi=4194304;function Tr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function po(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,a=n&268435455;if(a!==0){var s=a&~i;s!==0?r=Tr(s):(o&=a,o!==0&&(r=Tr(o)))}else a=n&~i,a!==0?r=Tr(a):o!==0&&(r=Tr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-et(t),i=1<<n,r|=e[n],t&=~i;return r}function Tm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Om(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var a=31-et(o),s=1<<a,l=i[a];l===-1?(!(s&n)||s&r)&&(i[a]=Tm(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}function ws(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function tp(){var e=Ti;return Ti<<=1,!(Ti&4194240)&&(Ti=64),e}function ba(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function fi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-et(t),e[t]=n}function Rm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-et(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function El(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-et(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var U=0;function np(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var rp,bl,ip,op,ap,xs=!1,Ri=[],Lt=null,$t=null,Nt=null,Jr=new Map,Qr=new Map,Pt=[],Im="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Nu(e,t){switch(e){case"focusin":case"focusout":Lt=null;break;case"dragenter":case"dragleave":$t=null;break;case"mouseover":case"mouseout":Nt=null;break;case"pointerover":case"pointerout":Jr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qr.delete(t.pointerId)}}function xr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=mi(t),t!==null&&bl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Am(e,t,n,r,i){switch(t){case"focusin":return Lt=xr(Lt,e,t,n,r,i),!0;case"dragenter":return $t=xr($t,e,t,n,r,i),!0;case"mouseover":return Nt=xr(Nt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Jr.set(o,xr(Jr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Qr.set(o,xr(Qr.get(o)||null,e,t,n,r,i)),!0}return!1}function sp(e){var t=sn(e.target);if(t!==null){var n=wn(t);if(n!==null){if(t=n.tag,t===13){if(t=Qd(n),t!==null){e.blockedOn=t,ap(e.priority,function(){ip(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Xi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=_s(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ms=r,n.target.dispatchEvent(r),ms=null}else return t=mi(n),t!==null&&bl(t),e.blockedOn=n,!1;t.shift()}return!0}function zu(e,t,n){Xi(e)&&n.delete(t)}function Dm(){xs=!1,Lt!==null&&Xi(Lt)&&(Lt=null),$t!==null&&Xi($t)&&($t=null),Nt!==null&&Xi(Nt)&&(Nt=null),Jr.forEach(zu),Qr.forEach(zu)}function _r(e,t){e.blockedOn===t&&(e.blockedOn=null,xs||(xs=!0,ze.unstable_scheduleCallback(ze.unstable_NormalPriority,Dm)))}function Gr(e){function t(i){return _r(i,e)}if(0<Ri.length){_r(Ri[0],e);for(var n=1;n<Ri.length;n++){var r=Ri[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Lt!==null&&_r(Lt,e),$t!==null&&_r($t,e),Nt!==null&&_r(Nt,e),Jr.forEach(t),Qr.forEach(t),n=0;n<Pt.length;n++)r=Pt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Pt.length&&(n=Pt[0],n.blockedOn===null);)sp(n),n.blockedOn===null&&Pt.shift()}var Qn=kt.ReactCurrentBatchConfig,fo=!0;function Lm(e,t,n,r){var i=U,o=Qn.transition;Qn.transition=null;try{U=1,jl(e,t,n,r)}finally{U=i,Qn.transition=o}}function $m(e,t,n,r){var i=U,o=Qn.transition;Qn.transition=null;try{U=4,jl(e,t,n,r)}finally{U=i,Qn.transition=o}}function jl(e,t,n,r){if(fo){var i=_s(e,t,n,r);if(i===null)La(e,t,r,ho,n),Nu(e,r);else if(Am(i,e,t,n,r))r.stopPropagation();else if(Nu(e,r),t&4&&-1<Im.indexOf(e)){for(;i!==null;){var o=mi(i);if(o!==null&&rp(o),o=_s(e,t,n,r),o===null&&La(e,t,r,ho,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else La(e,t,r,null,n)}}var ho=null;function _s(e,t,n,r){if(ho=null,e=kl(r),e=sn(e),e!==null)if(t=wn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Qd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ho=e,null}function lp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Sm()){case Sl:return 1;case Zd:return 4;case co:case Em:return 16;case ep:return 536870912;default:return 16}default:return 16}}var It=null,Cl=null,Yi=null;function up(){if(Yi)return Yi;var e,t=Cl,n=t.length,r,i="value"in It?It.value:It.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===i[o-r];r++);return Yi=i.slice(e,1<r?1-r:void 0)}function Zi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ii(){return!0}function Uu(){return!1}function Fe(e){function t(n,r,i,o,a){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=a,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(o):o[s]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Ii:Uu,this.isPropagationStopped=Uu,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ii)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ii)},persist:function(){},isPersistent:Ii}),t}var ur={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Pl=Fe(ur),hi=Y({},ur,{view:0,detail:0}),Nm=Fe(hi),ja,Ca,kr,Bo=Y({},hi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Tl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==kr&&(kr&&e.type==="mousemove"?(ja=e.screenX-kr.screenX,Ca=e.screenY-kr.screenY):Ca=ja=0,kr=e),ja)},movementY:function(e){return"movementY"in e?e.movementY:Ca}}),Fu=Fe(Bo),zm=Y({},Bo,{dataTransfer:0}),Um=Fe(zm),Fm=Y({},hi,{relatedTarget:0}),Pa=Fe(Fm),Mm=Y({},ur,{animationName:0,elapsedTime:0,pseudoElement:0}),Bm=Fe(Mm),Wm=Y({},ur,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),qm=Fe(Wm),Vm=Y({},ur,{data:0}),Mu=Fe(Vm),Hm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Km={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Jm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Jm[e])?!!t[e]:!1}function Tl(){return Qm}var Gm=Y({},hi,{key:function(e){if(e.key){var t=Hm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Zi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Km[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Tl,charCode:function(e){return e.type==="keypress"?Zi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Zi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Xm=Fe(Gm),Ym=Y({},Bo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Bu=Fe(Ym),Zm=Y({},hi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Tl}),ev=Fe(Zm),tv=Y({},ur,{propertyName:0,elapsedTime:0,pseudoElement:0}),nv=Fe(tv),rv=Y({},Bo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),iv=Fe(rv),ov=[9,13,27,32],Ol=yt&&"CompositionEvent"in window,Dr=null;yt&&"documentMode"in document&&(Dr=document.documentMode);var av=yt&&"TextEvent"in window&&!Dr,cp=yt&&(!Ol||Dr&&8<Dr&&11>=Dr),Wu=String.fromCharCode(32),qu=!1;function dp(e,t){switch(e){case"keyup":return ov.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function pp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var $n=!1;function sv(e,t){switch(e){case"compositionend":return pp(t);case"keypress":return t.which!==32?null:(qu=!0,Wu);case"textInput":return e=t.data,e===Wu&&qu?null:e;default:return null}}function lv(e,t){if($n)return e==="compositionend"||!Ol&&dp(e,t)?(e=up(),Yi=Cl=It=null,$n=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return cp&&t.locale!=="ko"?null:t.data;default:return null}}var uv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!uv[e.type]:t==="textarea"}function fp(e,t,n,r){qd(r),t=mo(t,"onChange"),0<t.length&&(n=new Pl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Lr=null,Xr=null;function cv(e){Ep(e,0)}function Wo(e){var t=Un(e);if(Nd(t))return e}function dv(e,t){if(e==="change")return t}var hp=!1;if(yt){var Ta;if(yt){var Oa="oninput"in document;if(!Oa){var Hu=document.createElement("div");Hu.setAttribute("oninput","return;"),Oa=typeof Hu.oninput=="function"}Ta=Oa}else Ta=!1;hp=Ta&&(!document.documentMode||9<document.documentMode)}function Ku(){Lr&&(Lr.detachEvent("onpropertychange",mp),Xr=Lr=null)}function mp(e){if(e.propertyName==="value"&&Wo(Xr)){var t=[];fp(t,Xr,e,kl(e)),Jd(cv,t)}}function pv(e,t,n){e==="focusin"?(Ku(),Lr=t,Xr=n,Lr.attachEvent("onpropertychange",mp)):e==="focusout"&&Ku()}function fv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Wo(Xr)}function hv(e,t){if(e==="click")return Wo(t)}function mv(e,t){if(e==="input"||e==="change")return Wo(t)}function vv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var nt=typeof Object.is=="function"?Object.is:vv;function Yr(e,t){if(nt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!rs.call(t,i)||!nt(e[i],t[i]))return!1}return!0}function Ju(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Qu(e,t){var n=Ju(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ju(n)}}function vp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?vp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function gp(){for(var e=window,t=so();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=so(e.document)}return t}function Rl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function gv(e){var t=gp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&vp(n.ownerDocument.documentElement,n)){if(r!==null&&Rl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Qu(n,o);var a=Qu(n,r);i&&a&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var yv=yt&&"documentMode"in document&&11>=document.documentMode,Nn=null,ks=null,$r=null,Ss=!1;function Gu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ss||Nn==null||Nn!==so(r)||(r=Nn,"selectionStart"in r&&Rl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),$r&&Yr($r,r)||($r=r,r=mo(ks,"onSelect"),0<r.length&&(t=new Pl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Nn)))}function Ai(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var zn={animationend:Ai("Animation","AnimationEnd"),animationiteration:Ai("Animation","AnimationIteration"),animationstart:Ai("Animation","AnimationStart"),transitionend:Ai("Transition","TransitionEnd")},Ra={},yp={};yt&&(yp=document.createElement("div").style,"AnimationEvent"in window||(delete zn.animationend.animation,delete zn.animationiteration.animation,delete zn.animationstart.animation),"TransitionEvent"in window||delete zn.transitionend.transition);function qo(e){if(Ra[e])return Ra[e];if(!zn[e])return e;var t=zn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in yp)return Ra[e]=t[n];return e}var wp=qo("animationend"),xp=qo("animationiteration"),_p=qo("animationstart"),kp=qo("transitionend"),Sp=new Map,Xu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ht(e,t){Sp.set(e,t),yn(t,[e])}for(var Ia=0;Ia<Xu.length;Ia++){var Aa=Xu[Ia],wv=Aa.toLowerCase(),xv=Aa[0].toUpperCase()+Aa.slice(1);Ht(wv,"on"+xv)}Ht(wp,"onAnimationEnd");Ht(xp,"onAnimationIteration");Ht(_p,"onAnimationStart");Ht("dblclick","onDoubleClick");Ht("focusin","onFocus");Ht("focusout","onBlur");Ht(kp,"onTransitionEnd");Yn("onMouseEnter",["mouseout","mouseover"]);Yn("onMouseLeave",["mouseout","mouseover"]);Yn("onPointerEnter",["pointerout","pointerover"]);Yn("onPointerLeave",["pointerout","pointerover"]);yn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));yn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));yn("onBeforeInput",["compositionend","keypress","textInput","paste"]);yn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));yn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));yn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),_v=new Set("cancel close invalid load scroll toggle".split(" ").concat(Or));function Yu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,wm(r,t,void 0,e),e.currentTarget=null}function Ep(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var a=r.length-1;0<=a;a--){var s=r[a],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&i.isPropagationStopped())break e;Yu(i,s,u),o=l}else for(a=0;a<r.length;a++){if(s=r[a],l=s.instance,u=s.currentTarget,s=s.listener,l!==o&&i.isPropagationStopped())break e;Yu(i,s,u),o=l}}}if(uo)throw e=ys,uo=!1,ys=null,e}function q(e,t){var n=t[Ps];n===void 0&&(n=t[Ps]=new Set);var r=e+"__bubble";n.has(r)||(bp(t,e,2,!1),n.add(r))}function Da(e,t,n){var r=0;t&&(r|=4),bp(n,e,r,t)}var Di="_reactListening"+Math.random().toString(36).slice(2);function Zr(e){if(!e[Di]){e[Di]=!0,Id.forEach(function(n){n!=="selectionchange"&&(_v.has(n)||Da(n,!1,e),Da(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Di]||(t[Di]=!0,Da("selectionchange",!1,t))}}function bp(e,t,n,r){switch(lp(t)){case 1:var i=Lm;break;case 4:i=$m;break;default:i=jl}n=i.bind(null,t,n,e),i=void 0,!gs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function La(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var s=r.stateNode.containerInfo;if(s===i||s.nodeType===8&&s.parentNode===i)break;if(a===4)for(a=r.return;a!==null;){var l=a.tag;if((l===3||l===4)&&(l=a.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;a=a.return}for(;s!==null;){if(a=sn(s),a===null)return;if(l=a.tag,l===5||l===6){r=o=a;continue e}s=s.parentNode}}r=r.return}Jd(function(){var u=o,c=kl(n),d=[];e:{var p=Sp.get(e);if(p!==void 0){var v=Pl,g=e;switch(e){case"keypress":if(Zi(n)===0)break e;case"keydown":case"keyup":v=Xm;break;case"focusin":g="focus",v=Pa;break;case"focusout":g="blur",v=Pa;break;case"beforeblur":case"afterblur":v=Pa;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=Um;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=ev;break;case wp:case xp:case _p:v=Bm;break;case kp:v=nv;break;case"scroll":v=Nm;break;case"wheel":v=iv;break;case"copy":case"cut":case"paste":v=qm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Bu}var w=(t&4)!==0,_=!w&&e==="scroll",m=w?p!==null?p+"Capture":null:p;w=[];for(var h=u,f;h!==null;){f=h;var y=f.stateNode;if(f.tag===5&&y!==null&&(f=y,m!==null&&(y=Kr(h,m),y!=null&&w.push(ei(h,y,f)))),_)break;h=h.return}0<w.length&&(p=new v(p,g,null,n,c),d.push({event:p,listeners:w}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",p&&n!==ms&&(g=n.relatedTarget||n.fromElement)&&(sn(g)||g[wt]))break e;if((v||p)&&(p=c.window===c?c:(p=c.ownerDocument)?p.defaultView||p.parentWindow:window,v?(g=n.relatedTarget||n.toElement,v=u,g=g?sn(g):null,g!==null&&(_=wn(g),g!==_||g.tag!==5&&g.tag!==6)&&(g=null)):(v=null,g=u),v!==g)){if(w=Fu,y="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(w=Bu,y="onPointerLeave",m="onPointerEnter",h="pointer"),_=v==null?p:Un(v),f=g==null?p:Un(g),p=new w(y,h+"leave",v,n,c),p.target=_,p.relatedTarget=f,y=null,sn(c)===u&&(w=new w(m,h+"enter",g,n,c),w.target=f,w.relatedTarget=_,y=w),_=y,v&&g)t:{for(w=v,m=g,h=0,f=w;f;f=Cn(f))h++;for(f=0,y=m;y;y=Cn(y))f++;for(;0<h-f;)w=Cn(w),h--;for(;0<f-h;)m=Cn(m),f--;for(;h--;){if(w===m||m!==null&&w===m.alternate)break t;w=Cn(w),m=Cn(m)}w=null}else w=null;v!==null&&Zu(d,p,v,w,!1),g!==null&&_!==null&&Zu(d,_,g,w,!0)}}e:{if(p=u?Un(u):window,v=p.nodeName&&p.nodeName.toLowerCase(),v==="select"||v==="input"&&p.type==="file")var S=dv;else if(Vu(p))if(hp)S=mv;else{S=fv;var b=pv}else(v=p.nodeName)&&v.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(S=hv);if(S&&(S=S(e,u))){fp(d,S,n,c);break e}b&&b(e,p,u),e==="focusout"&&(b=p._wrapperState)&&b.controlled&&p.type==="number"&&cs(p,"number",p.value)}switch(b=u?Un(u):window,e){case"focusin":(Vu(b)||b.contentEditable==="true")&&(Nn=b,ks=u,$r=null);break;case"focusout":$r=ks=Nn=null;break;case"mousedown":Ss=!0;break;case"contextmenu":case"mouseup":case"dragend":Ss=!1,Gu(d,n,c);break;case"selectionchange":if(yv)break;case"keydown":case"keyup":Gu(d,n,c)}var j;if(Ol)e:{switch(e){case"compositionstart":var O="onCompositionStart";break e;case"compositionend":O="onCompositionEnd";break e;case"compositionupdate":O="onCompositionUpdate";break e}O=void 0}else $n?dp(e,n)&&(O="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(O="onCompositionStart");O&&(cp&&n.locale!=="ko"&&($n||O!=="onCompositionStart"?O==="onCompositionEnd"&&$n&&(j=up()):(It=c,Cl="value"in It?It.value:It.textContent,$n=!0)),b=mo(u,O),0<b.length&&(O=new Mu(O,e,null,n,c),d.push({event:O,listeners:b}),j?O.data=j:(j=pp(n),j!==null&&(O.data=j)))),(j=av?sv(e,n):lv(e,n))&&(u=mo(u,"onBeforeInput"),0<u.length&&(c=new Mu("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=j))}Ep(d,t)})}function ei(e,t,n){return{instance:e,listener:t,currentTarget:n}}function mo(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Kr(e,n),o!=null&&r.unshift(ei(e,o,i)),o=Kr(e,t),o!=null&&r.push(ei(e,o,i))),e=e.return}return r}function Cn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Zu(e,t,n,r,i){for(var o=t._reactName,a=[];n!==null&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(l!==null&&l===r)break;s.tag===5&&u!==null&&(s=u,i?(l=Kr(n,o),l!=null&&a.unshift(ei(n,l,s))):i||(l=Kr(n,o),l!=null&&a.push(ei(n,l,s)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var kv=/\r\n?/g,Sv=/\u0000|\uFFFD/g;function ec(e){return(typeof e=="string"?e:""+e).replace(kv,`
`).replace(Sv,"")}function Li(e,t,n){if(t=ec(t),ec(e)!==t&&n)throw Error(E(425))}function vo(){}var Es=null,bs=null;function js(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Cs=typeof setTimeout=="function"?setTimeout:void 0,Ev=typeof clearTimeout=="function"?clearTimeout:void 0,tc=typeof Promise=="function"?Promise:void 0,bv=typeof queueMicrotask=="function"?queueMicrotask:typeof tc<"u"?function(e){return tc.resolve(null).then(e).catch(jv)}:Cs;function jv(e){setTimeout(function(){throw e})}function $a(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Gr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Gr(t)}function zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function nc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var cr=Math.random().toString(36).slice(2),st="__reactFiber$"+cr,ti="__reactProps$"+cr,wt="__reactContainer$"+cr,Ps="__reactEvents$"+cr,Cv="__reactListeners$"+cr,Pv="__reactHandles$"+cr;function sn(e){var t=e[st];if(t)return t;for(var n=e.parentNode;n;){if(t=n[wt]||n[st]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=nc(e);e!==null;){if(n=e[st])return n;e=nc(e)}return t}e=n,n=e.parentNode}return null}function mi(e){return e=e[st]||e[wt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Un(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(E(33))}function Vo(e){return e[ti]||null}var Ts=[],Fn=-1;function Kt(e){return{current:e}}function V(e){0>Fn||(e.current=Ts[Fn],Ts[Fn]=null,Fn--)}function W(e,t){Fn++,Ts[Fn]=e.current,e.current=t}var Vt={},ye=Kt(Vt),Re=Kt(!1),fn=Vt;function Zn(e,t){var n=e.type.contextTypes;if(!n)return Vt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ie(e){return e=e.childContextTypes,e!=null}function go(){V(Re),V(ye)}function rc(e,t,n){if(ye.current!==Vt)throw Error(E(168));W(ye,t),W(Re,n)}function jp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(E(108,pm(e)||"Unknown",i));return Y({},n,r)}function yo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Vt,fn=ye.current,W(ye,e),W(Re,Re.current),!0}function ic(e,t,n){var r=e.stateNode;if(!r)throw Error(E(169));n?(e=jp(e,t,fn),r.__reactInternalMemoizedMergedChildContext=e,V(Re),V(ye),W(ye,e)):V(Re),W(Re,n)}var ht=null,Ho=!1,Na=!1;function Cp(e){ht===null?ht=[e]:ht.push(e)}function Tv(e){Ho=!0,Cp(e)}function Jt(){if(!Na&&ht!==null){Na=!0;var e=0,t=U;try{var n=ht;for(U=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}ht=null,Ho=!1}catch(i){throw ht!==null&&(ht=ht.slice(e+1)),Yd(Sl,Jt),i}finally{U=t,Na=!1}}return null}var Mn=[],Bn=0,wo=null,xo=0,Me=[],Be=0,hn=null,mt=1,vt="";function tn(e,t){Mn[Bn++]=xo,Mn[Bn++]=wo,wo=e,xo=t}function Pp(e,t,n){Me[Be++]=mt,Me[Be++]=vt,Me[Be++]=hn,hn=e;var r=mt;e=vt;var i=32-et(r)-1;r&=~(1<<i),n+=1;var o=32-et(t)+i;if(30<o){var a=i-i%5;o=(r&(1<<a)-1).toString(32),r>>=a,i-=a,mt=1<<32-et(t)+i|n<<i|r,vt=o+e}else mt=1<<o|n<<i|r,vt=e}function Il(e){e.return!==null&&(tn(e,1),Pp(e,1,0))}function Al(e){for(;e===wo;)wo=Mn[--Bn],Mn[Bn]=null,xo=Mn[--Bn],Mn[Bn]=null;for(;e===hn;)hn=Me[--Be],Me[Be]=null,vt=Me[--Be],Me[Be]=null,mt=Me[--Be],Me[Be]=null}var Ne=null,$e=null,K=!1,Ze=null;function Tp(e,t){var n=We(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function oc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ne=e,$e=zt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ne=e,$e=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=hn!==null?{id:mt,overflow:vt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=We(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ne=e,$e=null,!0):!1;default:return!1}}function Os(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Rs(e){if(K){var t=$e;if(t){var n=t;if(!oc(e,t)){if(Os(e))throw Error(E(418));t=zt(n.nextSibling);var r=Ne;t&&oc(e,t)?Tp(r,n):(e.flags=e.flags&-4097|2,K=!1,Ne=e)}}else{if(Os(e))throw Error(E(418));e.flags=e.flags&-4097|2,K=!1,Ne=e}}}function ac(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ne=e}function $i(e){if(e!==Ne)return!1;if(!K)return ac(e),K=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!js(e.type,e.memoizedProps)),t&&(t=$e)){if(Os(e))throw Op(),Error(E(418));for(;t;)Tp(e,t),t=zt(t.nextSibling)}if(ac(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(E(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){$e=zt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}$e=null}}else $e=Ne?zt(e.stateNode.nextSibling):null;return!0}function Op(){for(var e=$e;e;)e=zt(e.nextSibling)}function er(){$e=Ne=null,K=!1}function Dl(e){Ze===null?Ze=[e]:Ze.push(e)}var Ov=kt.ReactCurrentBatchConfig;function Sr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(E(309));var r=n.stateNode}if(!r)throw Error(E(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(a){var s=i.refs;a===null?delete s[o]:s[o]=a},t._stringRef=o,t)}if(typeof e!="string")throw Error(E(284));if(!n._owner)throw Error(E(290,e))}return e}function Ni(e,t){throw e=Object.prototype.toString.call(t),Error(E(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function sc(e){var t=e._init;return t(e._payload)}function Rp(e){function t(m,h){if(e){var f=m.deletions;f===null?(m.deletions=[h],m.flags|=16):f.push(h)}}function n(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function r(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function i(m,h){return m=Bt(m,h),m.index=0,m.sibling=null,m}function o(m,h,f){return m.index=f,e?(f=m.alternate,f!==null?(f=f.index,f<h?(m.flags|=2,h):f):(m.flags|=2,h)):(m.flags|=1048576,h)}function a(m){return e&&m.alternate===null&&(m.flags|=2),m}function s(m,h,f,y){return h===null||h.tag!==6?(h=qa(f,m.mode,y),h.return=m,h):(h=i(h,f),h.return=m,h)}function l(m,h,f,y){var S=f.type;return S===Ln?c(m,h,f.props.children,y,f.key):h!==null&&(h.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===bt&&sc(S)===h.type)?(y=i(h,f.props),y.ref=Sr(m,h,f),y.return=m,y):(y=ao(f.type,f.key,f.props,null,m.mode,y),y.ref=Sr(m,h,f),y.return=m,y)}function u(m,h,f,y){return h===null||h.tag!==4||h.stateNode.containerInfo!==f.containerInfo||h.stateNode.implementation!==f.implementation?(h=Va(f,m.mode,y),h.return=m,h):(h=i(h,f.children||[]),h.return=m,h)}function c(m,h,f,y,S){return h===null||h.tag!==7?(h=pn(f,m.mode,y,S),h.return=m,h):(h=i(h,f),h.return=m,h)}function d(m,h,f){if(typeof h=="string"&&h!==""||typeof h=="number")return h=qa(""+h,m.mode,f),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case ji:return f=ao(h.type,h.key,h.props,null,m.mode,f),f.ref=Sr(m,null,h),f.return=m,f;case Dn:return h=Va(h,m.mode,f),h.return=m,h;case bt:var y=h._init;return d(m,y(h._payload),f)}if(Pr(h)||yr(h))return h=pn(h,m.mode,f,null),h.return=m,h;Ni(m,h)}return null}function p(m,h,f,y){var S=h!==null?h.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return S!==null?null:s(m,h,""+f,y);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case ji:return f.key===S?l(m,h,f,y):null;case Dn:return f.key===S?u(m,h,f,y):null;case bt:return S=f._init,p(m,h,S(f._payload),y)}if(Pr(f)||yr(f))return S!==null?null:c(m,h,f,y,null);Ni(m,f)}return null}function v(m,h,f,y,S){if(typeof y=="string"&&y!==""||typeof y=="number")return m=m.get(f)||null,s(h,m,""+y,S);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ji:return m=m.get(y.key===null?f:y.key)||null,l(h,m,y,S);case Dn:return m=m.get(y.key===null?f:y.key)||null,u(h,m,y,S);case bt:var b=y._init;return v(m,h,f,b(y._payload),S)}if(Pr(y)||yr(y))return m=m.get(f)||null,c(h,m,y,S,null);Ni(h,y)}return null}function g(m,h,f,y){for(var S=null,b=null,j=h,O=h=0,F=null;j!==null&&O<f.length;O++){j.index>O?(F=j,j=null):F=j.sibling;var D=p(m,j,f[O],y);if(D===null){j===null&&(j=F);break}e&&j&&D.alternate===null&&t(m,j),h=o(D,h,O),b===null?S=D:b.sibling=D,b=D,j=F}if(O===f.length)return n(m,j),K&&tn(m,O),S;if(j===null){for(;O<f.length;O++)j=d(m,f[O],y),j!==null&&(h=o(j,h,O),b===null?S=j:b.sibling=j,b=j);return K&&tn(m,O),S}for(j=r(m,j);O<f.length;O++)F=v(j,m,O,f[O],y),F!==null&&(e&&F.alternate!==null&&j.delete(F.key===null?O:F.key),h=o(F,h,O),b===null?S=F:b.sibling=F,b=F);return e&&j.forEach(function(Ee){return t(m,Ee)}),K&&tn(m,O),S}function w(m,h,f,y){var S=yr(f);if(typeof S!="function")throw Error(E(150));if(f=S.call(f),f==null)throw Error(E(151));for(var b=S=null,j=h,O=h=0,F=null,D=f.next();j!==null&&!D.done;O++,D=f.next()){j.index>O?(F=j,j=null):F=j.sibling;var Ee=p(m,j,D.value,y);if(Ee===null){j===null&&(j=F);break}e&&j&&Ee.alternate===null&&t(m,j),h=o(Ee,h,O),b===null?S=Ee:b.sibling=Ee,b=Ee,j=F}if(D.done)return n(m,j),K&&tn(m,O),S;if(j===null){for(;!D.done;O++,D=f.next())D=d(m,D.value,y),D!==null&&(h=o(D,h,O),b===null?S=D:b.sibling=D,b=D);return K&&tn(m,O),S}for(j=r(m,j);!D.done;O++,D=f.next())D=v(j,m,O,D.value,y),D!==null&&(e&&D.alternate!==null&&j.delete(D.key===null?O:D.key),h=o(D,h,O),b===null?S=D:b.sibling=D,b=D);return e&&j.forEach(function(ct){return t(m,ct)}),K&&tn(m,O),S}function _(m,h,f,y){if(typeof f=="object"&&f!==null&&f.type===Ln&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case ji:e:{for(var S=f.key,b=h;b!==null;){if(b.key===S){if(S=f.type,S===Ln){if(b.tag===7){n(m,b.sibling),h=i(b,f.props.children),h.return=m,m=h;break e}}else if(b.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===bt&&sc(S)===b.type){n(m,b.sibling),h=i(b,f.props),h.ref=Sr(m,b,f),h.return=m,m=h;break e}n(m,b);break}else t(m,b);b=b.sibling}f.type===Ln?(h=pn(f.props.children,m.mode,y,f.key),h.return=m,m=h):(y=ao(f.type,f.key,f.props,null,m.mode,y),y.ref=Sr(m,h,f),y.return=m,m=y)}return a(m);case Dn:e:{for(b=f.key;h!==null;){if(h.key===b)if(h.tag===4&&h.stateNode.containerInfo===f.containerInfo&&h.stateNode.implementation===f.implementation){n(m,h.sibling),h=i(h,f.children||[]),h.return=m,m=h;break e}else{n(m,h);break}else t(m,h);h=h.sibling}h=Va(f,m.mode,y),h.return=m,m=h}return a(m);case bt:return b=f._init,_(m,h,b(f._payload),y)}if(Pr(f))return g(m,h,f,y);if(yr(f))return w(m,h,f,y);Ni(m,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,h!==null&&h.tag===6?(n(m,h.sibling),h=i(h,f),h.return=m,m=h):(n(m,h),h=qa(f,m.mode,y),h.return=m,m=h),a(m)):n(m,h)}return _}var tr=Rp(!0),Ip=Rp(!1),_o=Kt(null),ko=null,Wn=null,Ll=null;function $l(){Ll=Wn=ko=null}function Nl(e){var t=_o.current;V(_o),e._currentValue=t}function Is(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Gn(e,t){ko=e,Ll=Wn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Oe=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(Ll!==e)if(e={context:e,memoizedValue:t,next:null},Wn===null){if(ko===null)throw Error(E(308));Wn=e,ko.dependencies={lanes:0,firstContext:e}}else Wn=Wn.next=e;return t}var ln=null;function zl(e){ln===null?ln=[e]:ln.push(e)}function Ap(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,zl(t)):(n.next=i.next,i.next=n),t.interleaved=n,xt(e,r)}function xt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var jt=!1;function Ul(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Dp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function gt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ut(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,xt(e,n)}return i=r.interleaved,i===null?(t.next=t,zl(r)):(t.next=i.next,i.next=t),r.interleaved=t,xt(e,n)}function eo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,El(e,n)}}function lc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=a:o=o.next=a,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function So(e,t,n,r){var i=e.updateQueue;jt=!1;var o=i.firstBaseUpdate,a=i.lastBaseUpdate,s=i.shared.pending;if(s!==null){i.shared.pending=null;var l=s,u=l.next;l.next=null,a===null?o=u:a.next=u,a=l;var c=e.alternate;c!==null&&(c=c.updateQueue,s=c.lastBaseUpdate,s!==a&&(s===null?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(o!==null){var d=i.baseState;a=0,c=u=l=null,s=o;do{var p=s.lane,v=s.eventTime;if((r&p)===p){c!==null&&(c=c.next={eventTime:v,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var g=e,w=s;switch(p=t,v=n,w.tag){case 1:if(g=w.payload,typeof g=="function"){d=g.call(v,d,p);break e}d=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=w.payload,p=typeof g=="function"?g.call(v,d,p):g,p==null)break e;d=Y({},d,p);break e;case 2:jt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[s]:p.push(s))}else v={eventTime:v,lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},c===null?(u=c=v,l=d):c=c.next=v,a|=p;if(s=s.next,s===null){if(s=i.shared.pending,s===null)break;p=s,s=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(1);if(c===null&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do a|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);vn|=a,e.lanes=a,e.memoizedState=d}}function uc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(E(191,i));i.call(r)}}}var vi={},ut=Kt(vi),ni=Kt(vi),ri=Kt(vi);function un(e){if(e===vi)throw Error(E(174));return e}function Fl(e,t){switch(W(ri,t),W(ni,e),W(ut,vi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ps(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ps(t,e)}V(ut),W(ut,t)}function nr(){V(ut),V(ni),V(ri)}function Lp(e){un(ri.current);var t=un(ut.current),n=ps(t,e.type);t!==n&&(W(ni,e),W(ut,n))}function Ml(e){ni.current===e&&(V(ut),V(ni))}var G=Kt(0);function Eo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var za=[];function Bl(){for(var e=0;e<za.length;e++)za[e]._workInProgressVersionPrimary=null;za.length=0}var to=kt.ReactCurrentDispatcher,Ua=kt.ReactCurrentBatchConfig,mn=0,X=null,ie=null,ue=null,bo=!1,Nr=!1,ii=0,Rv=0;function he(){throw Error(E(321))}function Wl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!nt(e[n],t[n]))return!1;return!0}function ql(e,t,n,r,i,o){if(mn=o,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,to.current=e===null||e.memoizedState===null?Lv:$v,e=n(r,i),Nr){o=0;do{if(Nr=!1,ii=0,25<=o)throw Error(E(301));o+=1,ue=ie=null,t.updateQueue=null,to.current=Nv,e=n(r,i)}while(Nr)}if(to.current=jo,t=ie!==null&&ie.next!==null,mn=0,ue=ie=X=null,bo=!1,t)throw Error(E(300));return e}function Vl(){var e=ii!==0;return ii=0,e}function at(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ue===null?X.memoizedState=ue=e:ue=ue.next=e,ue}function Ke(){if(ie===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=ie.next;var t=ue===null?X.memoizedState:ue.next;if(t!==null)ue=t,ie=e;else{if(e===null)throw Error(E(310));ie=e,e={memoizedState:ie.memoizedState,baseState:ie.baseState,baseQueue:ie.baseQueue,queue:ie.queue,next:null},ue===null?X.memoizedState=ue=e:ue=ue.next=e}return ue}function oi(e,t){return typeof t=="function"?t(e):t}function Fa(e){var t=Ke(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=ie,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var a=i.next;i.next=o.next,o.next=a}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var s=a=null,l=null,u=o;do{var c=u.lane;if((mn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(s=l=d,a=r):l=l.next=d,X.lanes|=c,vn|=c}u=u.next}while(u!==null&&u!==o);l===null?a=r:l.next=s,nt(r,t.memoizedState)||(Oe=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,X.lanes|=o,vn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ma(e){var t=Ke(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do o=e(o,a.action),a=a.next;while(a!==i);nt(o,t.memoizedState)||(Oe=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function $p(){}function Np(e,t){var n=X,r=Ke(),i=t(),o=!nt(r.memoizedState,i);if(o&&(r.memoizedState=i,Oe=!0),r=r.queue,Hl(Fp.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ue!==null&&ue.memoizedState.tag&1){if(n.flags|=2048,ai(9,Up.bind(null,n,r,i,t),void 0,null),ce===null)throw Error(E(349));mn&30||zp(n,t,i)}return i}function zp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Up(e,t,n,r){t.value=n,t.getSnapshot=r,Mp(t)&&Bp(e)}function Fp(e,t,n){return n(function(){Mp(t)&&Bp(e)})}function Mp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!nt(e,n)}catch{return!0}}function Bp(e){var t=xt(e,1);t!==null&&tt(t,e,1,-1)}function cc(e){var t=at();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:oi,lastRenderedState:e},t.queue=e,e=e.dispatch=Dv.bind(null,X,e),[t.memoizedState,e]}function ai(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Wp(){return Ke().memoizedState}function no(e,t,n,r){var i=at();X.flags|=e,i.memoizedState=ai(1|t,n,void 0,r===void 0?null:r)}function Ko(e,t,n,r){var i=Ke();r=r===void 0?null:r;var o=void 0;if(ie!==null){var a=ie.memoizedState;if(o=a.destroy,r!==null&&Wl(r,a.deps)){i.memoizedState=ai(t,n,o,r);return}}X.flags|=e,i.memoizedState=ai(1|t,n,o,r)}function dc(e,t){return no(8390656,8,e,t)}function Hl(e,t){return Ko(2048,8,e,t)}function qp(e,t){return Ko(4,2,e,t)}function Vp(e,t){return Ko(4,4,e,t)}function Hp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Kp(e,t,n){return n=n!=null?n.concat([e]):null,Ko(4,4,Hp.bind(null,t,e),n)}function Kl(){}function Jp(e,t){var n=Ke();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Wl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Qp(e,t){var n=Ke();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Wl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Gp(e,t,n){return mn&21?(nt(n,t)||(n=tp(),X.lanes|=n,vn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Oe=!0),e.memoizedState=n)}function Iv(e,t){var n=U;U=n!==0&&4>n?n:4,e(!0);var r=Ua.transition;Ua.transition={};try{e(!1),t()}finally{U=n,Ua.transition=r}}function Xp(){return Ke().memoizedState}function Av(e,t,n){var r=Mt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yp(e))Zp(t,n);else if(n=Ap(e,t,n,r),n!==null){var i=_e();tt(n,e,r,i),ef(n,t,r)}}function Dv(e,t,n){var r=Mt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yp(e))Zp(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var a=t.lastRenderedState,s=o(a,n);if(i.hasEagerState=!0,i.eagerState=s,nt(s,a)){var l=t.interleaved;l===null?(i.next=i,zl(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=Ap(e,t,i,r),n!==null&&(i=_e(),tt(n,e,r,i),ef(n,t,r))}}function Yp(e){var t=e.alternate;return e===X||t!==null&&t===X}function Zp(e,t){Nr=bo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function ef(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,El(e,n)}}var jo={readContext:He,useCallback:he,useContext:he,useEffect:he,useImperativeHandle:he,useInsertionEffect:he,useLayoutEffect:he,useMemo:he,useReducer:he,useRef:he,useState:he,useDebugValue:he,useDeferredValue:he,useTransition:he,useMutableSource:he,useSyncExternalStore:he,useId:he,unstable_isNewReconciler:!1},Lv={readContext:He,useCallback:function(e,t){return at().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:dc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,no(4194308,4,Hp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return no(4194308,4,e,t)},useInsertionEffect:function(e,t){return no(4,2,e,t)},useMemo:function(e,t){var n=at();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=at();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Av.bind(null,X,e),[r.memoizedState,e]},useRef:function(e){var t=at();return e={current:e},t.memoizedState=e},useState:cc,useDebugValue:Kl,useDeferredValue:function(e){return at().memoizedState=e},useTransition:function(){var e=cc(!1),t=e[0];return e=Iv.bind(null,e[1]),at().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=X,i=at();if(K){if(n===void 0)throw Error(E(407));n=n()}else{if(n=t(),ce===null)throw Error(E(349));mn&30||zp(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,dc(Fp.bind(null,r,o,e),[e]),r.flags|=2048,ai(9,Up.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=at(),t=ce.identifierPrefix;if(K){var n=vt,r=mt;n=(r&~(1<<32-et(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ii++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Rv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},$v={readContext:He,useCallback:Jp,useContext:He,useEffect:Hl,useImperativeHandle:Kp,useInsertionEffect:qp,useLayoutEffect:Vp,useMemo:Qp,useReducer:Fa,useRef:Wp,useState:function(){return Fa(oi)},useDebugValue:Kl,useDeferredValue:function(e){var t=Ke();return Gp(t,ie.memoizedState,e)},useTransition:function(){var e=Fa(oi)[0],t=Ke().memoizedState;return[e,t]},useMutableSource:$p,useSyncExternalStore:Np,useId:Xp,unstable_isNewReconciler:!1},Nv={readContext:He,useCallback:Jp,useContext:He,useEffect:Hl,useImperativeHandle:Kp,useInsertionEffect:qp,useLayoutEffect:Vp,useMemo:Qp,useReducer:Ma,useRef:Wp,useState:function(){return Ma(oi)},useDebugValue:Kl,useDeferredValue:function(e){var t=Ke();return ie===null?t.memoizedState=e:Gp(t,ie.memoizedState,e)},useTransition:function(){var e=Ma(oi)[0],t=Ke().memoizedState;return[e,t]},useMutableSource:$p,useSyncExternalStore:Np,useId:Xp,unstable_isNewReconciler:!1};function Ge(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function As(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Jo={isMounted:function(e){return(e=e._reactInternals)?wn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=_e(),i=Mt(e),o=gt(r,i);o.payload=t,n!=null&&(o.callback=n),t=Ut(e,o,i),t!==null&&(tt(t,e,i,r),eo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=_e(),i=Mt(e),o=gt(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Ut(e,o,i),t!==null&&(tt(t,e,i,r),eo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=_e(),r=Mt(e),i=gt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Ut(e,i,r),t!==null&&(tt(t,e,r,n),eo(t,e,r))}};function pc(e,t,n,r,i,o,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,a):t.prototype&&t.prototype.isPureReactComponent?!Yr(n,r)||!Yr(i,o):!0}function tf(e,t,n){var r=!1,i=Vt,o=t.contextType;return typeof o=="object"&&o!==null?o=He(o):(i=Ie(t)?fn:ye.current,r=t.contextTypes,o=(r=r!=null)?Zn(e,i):Vt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Jo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function fc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Jo.enqueueReplaceState(t,t.state,null)}function Ds(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Ul(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=He(o):(o=Ie(t)?fn:ye.current,i.context=Zn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(As(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Jo.enqueueReplaceState(i,i.state,null),So(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function rr(e,t){try{var n="",r=t;do n+=dm(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Ba(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ls(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var zv=typeof WeakMap=="function"?WeakMap:Map;function nf(e,t,n){n=gt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Po||(Po=!0,Vs=r),Ls(e,t)},n}function rf(e,t,n){n=gt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Ls(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Ls(e,t),typeof r!="function"&&(Ft===null?Ft=new Set([this]):Ft.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function hc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new zv;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Yv.bind(null,e,t,n),t.then(e,e))}function mc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function vc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=gt(-1,1),t.tag=2,Ut(n,t,1))),n.lanes|=1),e)}var Uv=kt.ReactCurrentOwner,Oe=!1;function xe(e,t,n,r){t.child=e===null?Ip(t,null,n,r):tr(t,e.child,n,r)}function gc(e,t,n,r,i){n=n.render;var o=t.ref;return Gn(t,i),r=ql(e,t,n,r,o,i),n=Vl(),e!==null&&!Oe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,_t(e,t,i)):(K&&n&&Il(t),t.flags|=1,xe(e,t,r,i),t.child)}function yc(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!tu(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,of(e,t,o,r,i)):(e=ao(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var a=o.memoizedProps;if(n=n.compare,n=n!==null?n:Yr,n(a,r)&&e.ref===t.ref)return _t(e,t,i)}return t.flags|=1,e=Bt(o,r),e.ref=t.ref,e.return=t,t.child=e}function of(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Yr(o,r)&&e.ref===t.ref)if(Oe=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Oe=!0);else return t.lanes=e.lanes,_t(e,t,i)}return $s(e,t,n,r,i)}function af(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},W(Vn,Le),Le|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,W(Vn,Le),Le|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,W(Vn,Le),Le|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,W(Vn,Le),Le|=r;return xe(e,t,i,n),t.child}function sf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function $s(e,t,n,r,i){var o=Ie(n)?fn:ye.current;return o=Zn(t,o),Gn(t,i),n=ql(e,t,n,r,o,i),r=Vl(),e!==null&&!Oe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,_t(e,t,i)):(K&&r&&Il(t),t.flags|=1,xe(e,t,n,i),t.child)}function wc(e,t,n,r,i){if(Ie(n)){var o=!0;yo(t)}else o=!1;if(Gn(t,i),t.stateNode===null)ro(e,t),tf(t,n,r),Ds(t,n,r,i),r=!0;else if(e===null){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=He(u):(u=Ie(n)?fn:ye.current,u=Zn(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof a.getSnapshotBeforeUpdate=="function";d||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(s!==r||l!==u)&&fc(t,a,r,u),jt=!1;var p=t.memoizedState;a.state=p,So(t,r,a,i),l=t.memoizedState,s!==r||p!==l||Re.current||jt?(typeof c=="function"&&(As(t,n,c,r),l=t.memoizedState),(s=jt||pc(t,n,s,r,p,l,u))?(d||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=u,r=s):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Dp(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:Ge(t.type,s),a.props=u,d=t.pendingProps,p=a.context,l=n.contextType,typeof l=="object"&&l!==null?l=He(l):(l=Ie(n)?fn:ye.current,l=Zn(t,l));var v=n.getDerivedStateFromProps;(c=typeof v=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(s!==d||p!==l)&&fc(t,a,r,l),jt=!1,p=t.memoizedState,a.state=p,So(t,r,a,i);var g=t.memoizedState;s!==d||p!==g||Re.current||jt?(typeof v=="function"&&(As(t,n,v,r),g=t.memoizedState),(u=jt||pc(t,n,u,r,p,g,l)||!1)?(c||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,g,l),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,g,l)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),a.props=r,a.state=g,a.context=l,r=u):(typeof a.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Ns(e,t,n,r,o,i)}function Ns(e,t,n,r,i,o){sf(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return i&&ic(t,n,!1),_t(e,t,o);r=t.stateNode,Uv.current=t;var s=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=tr(t,e.child,null,o),t.child=tr(t,null,s,o)):xe(e,t,s,o),t.memoizedState=r.state,i&&ic(t,n,!0),t.child}function lf(e){var t=e.stateNode;t.pendingContext?rc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&rc(e,t.context,!1),Fl(e,t.containerInfo)}function xc(e,t,n,r,i){return er(),Dl(i),t.flags|=256,xe(e,t,n,r),t.child}var zs={dehydrated:null,treeContext:null,retryLane:0};function Us(e){return{baseLanes:e,cachePool:null,transitions:null}}function uf(e,t,n){var r=t.pendingProps,i=G.current,o=!1,a=(t.flags&128)!==0,s;if((s=a)||(s=e!==null&&e.memoizedState===null?!1:(i&2)!==0),s?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),W(G,i&1),e===null)return Rs(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,o?(r=t.mode,o=t.child,a={mode:"hidden",children:a},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=a):o=Xo(a,r,0,null),e=pn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Us(n),t.memoizedState=zs,e):Jl(t,a));if(i=e.memoizedState,i!==null&&(s=i.dehydrated,s!==null))return Fv(e,t,a,r,s,i,n);if(o){o=r.fallback,a=t.mode,i=e.child,s=i.sibling;var l={mode:"hidden",children:r.children};return!(a&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Bt(i,l),r.subtreeFlags=i.subtreeFlags&14680064),s!==null?o=Bt(s,o):(o=pn(o,a,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,a=e.child.memoizedState,a=a===null?Us(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},o.memoizedState=a,o.childLanes=e.childLanes&~n,t.memoizedState=zs,r}return o=e.child,e=o.sibling,r=Bt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Jl(e,t){return t=Xo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function zi(e,t,n,r){return r!==null&&Dl(r),tr(t,e.child,null,n),e=Jl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Fv(e,t,n,r,i,o,a){if(n)return t.flags&256?(t.flags&=-257,r=Ba(Error(E(422))),zi(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Xo({mode:"visible",children:r.children},i,0,null),o=pn(o,i,a,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&tr(t,e.child,null,a),t.child.memoizedState=Us(a),t.memoizedState=zs,o);if(!(t.mode&1))return zi(e,t,a,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var s=r.dgst;return r=s,o=Error(E(419)),r=Ba(o,r,void 0),zi(e,t,a,r)}if(s=(a&e.childLanes)!==0,Oe||s){if(r=ce,r!==null){switch(a&-a){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|a)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,xt(e,i),tt(r,e,i,-1))}return eu(),r=Ba(Error(E(421))),zi(e,t,a,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Zv.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,$e=zt(i.nextSibling),Ne=t,K=!0,Ze=null,e!==null&&(Me[Be++]=mt,Me[Be++]=vt,Me[Be++]=hn,mt=e.id,vt=e.overflow,hn=t),t=Jl(t,r.children),t.flags|=4096,t)}function _c(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Is(e.return,t,n)}function Wa(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function cf(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(xe(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&_c(e,n,t);else if(e.tag===19)_c(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(W(G,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Eo(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Wa(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Eo(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Wa(t,!0,n,null,o);break;case"together":Wa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ro(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function _t(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),vn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(E(153));if(t.child!==null){for(e=t.child,n=Bt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Bt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Mv(e,t,n){switch(t.tag){case 3:lf(t),er();break;case 5:Lp(t);break;case 1:Ie(t.type)&&yo(t);break;case 4:Fl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;W(_o,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(W(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?uf(e,t,n):(W(G,G.current&1),e=_t(e,t,n),e!==null?e.sibling:null);W(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return cf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),W(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,af(e,t,n)}return _t(e,t,n)}var df,Fs,pf,ff;df=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Fs=function(){};pf=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,un(ut.current);var o=null;switch(n){case"input":i=ls(e,i),r=ls(e,r),o=[];break;case"select":i=Y({},i,{value:void 0}),r=Y({},r,{value:void 0}),o=[];break;case"textarea":i=ds(e,i),r=ds(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=vo)}fs(n,r);var a;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var s=i[u];for(a in s)s.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Vr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(s=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==s&&(l!=null||s!=null))if(u==="style")if(s){for(a in s)!s.hasOwnProperty(a)||l&&l.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in l)l.hasOwnProperty(a)&&s[a]!==l[a]&&(n||(n={}),n[a]=l[a])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,s=s?s.__html:void 0,l!=null&&s!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Vr.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&q("scroll",e),o||s===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};ff=function(e,t,n,r){n!==r&&(t.flags|=4)};function Er(e,t){if(!K)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function me(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Bv(e,t,n){var r=t.pendingProps;switch(Al(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return me(t),null;case 1:return Ie(t.type)&&go(),me(t),null;case 3:return r=t.stateNode,nr(),V(Re),V(ye),Bl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&($i(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ze!==null&&(Js(Ze),Ze=null))),Fs(e,t),me(t),null;case 5:Ml(t);var i=un(ri.current);if(n=t.type,e!==null&&t.stateNode!=null)pf(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(E(166));return me(t),null}if(e=un(ut.current),$i(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[st]=t,r[ti]=o,e=(t.mode&1)!==0,n){case"dialog":q("cancel",r),q("close",r);break;case"iframe":case"object":case"embed":q("load",r);break;case"video":case"audio":for(i=0;i<Or.length;i++)q(Or[i],r);break;case"source":q("error",r);break;case"img":case"image":case"link":q("error",r),q("load",r);break;case"details":q("toggle",r);break;case"input":Ou(r,o),q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},q("invalid",r);break;case"textarea":Iu(r,o),q("invalid",r)}fs(n,o),i=null;for(var a in o)if(o.hasOwnProperty(a)){var s=o[a];a==="children"?typeof s=="string"?r.textContent!==s&&(o.suppressHydrationWarning!==!0&&Li(r.textContent,s,e),i=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(o.suppressHydrationWarning!==!0&&Li(r.textContent,s,e),i=["children",""+s]):Vr.hasOwnProperty(a)&&s!=null&&a==="onScroll"&&q("scroll",r)}switch(n){case"input":Ci(r),Ru(r,o,!0);break;case"textarea":Ci(r),Au(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=vo)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Fd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[st]=t,e[ti]=r,df(e,t,!1,!1),t.stateNode=e;e:{switch(a=hs(n,r),n){case"dialog":q("cancel",e),q("close",e),i=r;break;case"iframe":case"object":case"embed":q("load",e),i=r;break;case"video":case"audio":for(i=0;i<Or.length;i++)q(Or[i],e);i=r;break;case"source":q("error",e),i=r;break;case"img":case"image":case"link":q("error",e),q("load",e),i=r;break;case"details":q("toggle",e),i=r;break;case"input":Ou(e,r),i=ls(e,r),q("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Y({},r,{value:void 0}),q("invalid",e);break;case"textarea":Iu(e,r),i=ds(e,r),q("invalid",e);break;default:i=r}fs(n,i),s=i;for(o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="style"?Wd(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Md(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Hr(e,l):typeof l=="number"&&Hr(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Vr.hasOwnProperty(o)?l!=null&&o==="onScroll"&&q("scroll",e):l!=null&&yl(e,o,l,a))}switch(n){case"input":Ci(e),Ru(e,r,!1);break;case"textarea":Ci(e),Au(e);break;case"option":r.value!=null&&e.setAttribute("value",""+qt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Hn(e,!!r.multiple,o,!1):r.defaultValue!=null&&Hn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=vo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return me(t),null;case 6:if(e&&t.stateNode!=null)ff(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(E(166));if(n=un(ri.current),un(ut.current),$i(t)){if(r=t.stateNode,n=t.memoizedProps,r[st]=t,(o=r.nodeValue!==n)&&(e=Ne,e!==null))switch(e.tag){case 3:Li(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Li(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[st]=t,t.stateNode=r}return me(t),null;case 13:if(V(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(K&&$e!==null&&t.mode&1&&!(t.flags&128))Op(),er(),t.flags|=98560,o=!1;else if(o=$i(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(E(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(E(317));o[st]=t}else er(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;me(t),o=!1}else Ze!==null&&(Js(Ze),Ze=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?oe===0&&(oe=3):eu())),t.updateQueue!==null&&(t.flags|=4),me(t),null);case 4:return nr(),Fs(e,t),e===null&&Zr(t.stateNode.containerInfo),me(t),null;case 10:return Nl(t.type._context),me(t),null;case 17:return Ie(t.type)&&go(),me(t),null;case 19:if(V(G),o=t.memoizedState,o===null)return me(t),null;if(r=(t.flags&128)!==0,a=o.rendering,a===null)if(r)Er(o,!1);else{if(oe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Eo(e),a!==null){for(t.flags|=128,Er(o,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,a=o.alternate,a===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=a.childLanes,o.lanes=a.lanes,o.child=a.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=a.memoizedProps,o.memoizedState=a.memoizedState,o.updateQueue=a.updateQueue,o.type=a.type,e=a.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return W(G,G.current&1|2),t.child}e=e.sibling}o.tail!==null&&ee()>ir&&(t.flags|=128,r=!0,Er(o,!1),t.lanes=4194304)}else{if(!r)if(e=Eo(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Er(o,!0),o.tail===null&&o.tailMode==="hidden"&&!a.alternate&&!K)return me(t),null}else 2*ee()-o.renderingStartTime>ir&&n!==1073741824&&(t.flags|=128,r=!0,Er(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(n=o.last,n!==null?n.sibling=a:t.child=a,o.last=a)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ee(),t.sibling=null,n=G.current,W(G,r?n&1|2:n&1),t):(me(t),null);case 22:case 23:return Zl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Le&1073741824&&(me(t),t.subtreeFlags&6&&(t.flags|=8192)):me(t),null;case 24:return null;case 25:return null}throw Error(E(156,t.tag))}function Wv(e,t){switch(Al(t),t.tag){case 1:return Ie(t.type)&&go(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return nr(),V(Re),V(ye),Bl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ml(t),null;case 13:if(V(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(E(340));er()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(G),null;case 4:return nr(),null;case 10:return Nl(t.type._context),null;case 22:case 23:return Zl(),null;case 24:return null;default:return null}}var Ui=!1,ge=!1,qv=typeof WeakSet=="function"?WeakSet:Set,T=null;function qn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function Ms(e,t,n){try{n()}catch(r){Z(e,t,r)}}var kc=!1;function Vv(e,t){if(Es=fo,e=gp(),Rl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var a=0,s=-1,l=-1,u=0,c=0,d=e,p=null;t:for(;;){for(var v;d!==n||i!==0&&d.nodeType!==3||(s=a+i),d!==o||r!==0&&d.nodeType!==3||(l=a+r),d.nodeType===3&&(a+=d.nodeValue.length),(v=d.firstChild)!==null;)p=d,d=v;for(;;){if(d===e)break t;if(p===n&&++u===i&&(s=a),p===o&&++c===r&&(l=a),(v=d.nextSibling)!==null)break;d=p,p=d.parentNode}d=v}n=s===-1||l===-1?null:{start:s,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(bs={focusedElem:e,selectionRange:n},fo=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var w=g.memoizedProps,_=g.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?w:Ge(t.type,w),_);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(E(163))}}catch(y){Z(t,t.return,y)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return g=kc,kc=!1,g}function zr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&Ms(t,n,o)}i=i.next}while(i!==r)}}function Qo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Bs(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function hf(e){var t=e.alternate;t!==null&&(e.alternate=null,hf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[st],delete t[ti],delete t[Ps],delete t[Cv],delete t[Pv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mf(e){return e.tag===5||e.tag===3||e.tag===4}function Sc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ws(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=vo));else if(r!==4&&(e=e.child,e!==null))for(Ws(e,t,n),e=e.sibling;e!==null;)Ws(e,t,n),e=e.sibling}function qs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(qs(e,t,n),e=e.sibling;e!==null;)qs(e,t,n),e=e.sibling}var de=null,Xe=!1;function St(e,t,n){for(n=n.child;n!==null;)vf(e,t,n),n=n.sibling}function vf(e,t,n){if(lt&&typeof lt.onCommitFiberUnmount=="function")try{lt.onCommitFiberUnmount(Mo,n)}catch{}switch(n.tag){case 5:ge||qn(n,t);case 6:var r=de,i=Xe;de=null,St(e,t,n),de=r,Xe=i,de!==null&&(Xe?(e=de,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):de.removeChild(n.stateNode));break;case 18:de!==null&&(Xe?(e=de,n=n.stateNode,e.nodeType===8?$a(e.parentNode,n):e.nodeType===1&&$a(e,n),Gr(e)):$a(de,n.stateNode));break;case 4:r=de,i=Xe,de=n.stateNode.containerInfo,Xe=!0,St(e,t,n),de=r,Xe=i;break;case 0:case 11:case 14:case 15:if(!ge&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,a=o.destroy;o=o.tag,a!==void 0&&(o&2||o&4)&&Ms(n,t,a),i=i.next}while(i!==r)}St(e,t,n);break;case 1:if(!ge&&(qn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Z(n,t,s)}St(e,t,n);break;case 21:St(e,t,n);break;case 22:n.mode&1?(ge=(r=ge)||n.memoizedState!==null,St(e,t,n),ge=r):St(e,t,n);break;default:St(e,t,n)}}function Ec(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new qv),t.forEach(function(r){var i=eg.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Je(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,a=t,s=a;e:for(;s!==null;){switch(s.tag){case 5:de=s.stateNode,Xe=!1;break e;case 3:de=s.stateNode.containerInfo,Xe=!0;break e;case 4:de=s.stateNode.containerInfo,Xe=!0;break e}s=s.return}if(de===null)throw Error(E(160));vf(o,a,i),de=null,Xe=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){Z(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)gf(t,e),t=t.sibling}function gf(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Je(t,e),it(e),r&4){try{zr(3,e,e.return),Qo(3,e)}catch(w){Z(e,e.return,w)}try{zr(5,e,e.return)}catch(w){Z(e,e.return,w)}}break;case 1:Je(t,e),it(e),r&512&&n!==null&&qn(n,n.return);break;case 5:if(Je(t,e),it(e),r&512&&n!==null&&qn(n,n.return),e.flags&32){var i=e.stateNode;try{Hr(i,"")}catch(w){Z(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,a=n!==null?n.memoizedProps:o,s=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{s==="input"&&o.type==="radio"&&o.name!=null&&zd(i,o),hs(s,a);var u=hs(s,o);for(a=0;a<l.length;a+=2){var c=l[a],d=l[a+1];c==="style"?Wd(i,d):c==="dangerouslySetInnerHTML"?Md(i,d):c==="children"?Hr(i,d):yl(i,c,d,u)}switch(s){case"input":us(i,o);break;case"textarea":Ud(i,o);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var v=o.value;v!=null?Hn(i,!!o.multiple,v,!1):p!==!!o.multiple&&(o.defaultValue!=null?Hn(i,!!o.multiple,o.defaultValue,!0):Hn(i,!!o.multiple,o.multiple?[]:"",!1))}i[ti]=o}catch(w){Z(e,e.return,w)}}break;case 6:if(Je(t,e),it(e),r&4){if(e.stateNode===null)throw Error(E(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(w){Z(e,e.return,w)}}break;case 3:if(Je(t,e),it(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Gr(t.containerInfo)}catch(w){Z(e,e.return,w)}break;case 4:Je(t,e),it(e);break;case 13:Je(t,e),it(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(Xl=ee())),r&4&&Ec(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(ge=(u=ge)||c,Je(t,e),ge=u):Je(t,e),it(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(T=e,c=e.child;c!==null;){for(d=T=c;T!==null;){switch(p=T,v=p.child,p.tag){case 0:case 11:case 14:case 15:zr(4,p,p.return);break;case 1:qn(p,p.return);var g=p.stateNode;if(typeof g.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(w){Z(r,n,w)}}break;case 5:qn(p,p.return);break;case 22:if(p.memoizedState!==null){jc(d);continue}}v!==null?(v.return=p,T=v):jc(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(s=d.stateNode,l=d.memoizedProps.style,a=l!=null&&l.hasOwnProperty("display")?l.display:null,s.style.display=Bd("display",a))}catch(w){Z(e,e.return,w)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(w){Z(e,e.return,w)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Je(t,e),it(e),r&4&&Ec(e);break;case 21:break;default:Je(t,e),it(e)}}function it(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(mf(n)){var r=n;break e}n=n.return}throw Error(E(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Hr(i,""),r.flags&=-33);var o=Sc(e);qs(e,o,i);break;case 3:case 4:var a=r.stateNode.containerInfo,s=Sc(e);Ws(e,s,a);break;default:throw Error(E(161))}}catch(l){Z(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Hv(e,t,n){T=e,yf(e)}function yf(e,t,n){for(var r=(e.mode&1)!==0;T!==null;){var i=T,o=i.child;if(i.tag===22&&r){var a=i.memoizedState!==null||Ui;if(!a){var s=i.alternate,l=s!==null&&s.memoizedState!==null||ge;s=Ui;var u=ge;if(Ui=a,(ge=l)&&!u)for(T=i;T!==null;)a=T,l=a.child,a.tag===22&&a.memoizedState!==null?Cc(i):l!==null?(l.return=a,T=l):Cc(i);for(;o!==null;)T=o,yf(o),o=o.sibling;T=i,Ui=s,ge=u}bc(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,T=o):bc(e)}}function bc(e){for(;T!==null;){var t=T;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ge||Qo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ge)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ge(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&uc(t,o,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}uc(t,a,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Gr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(E(163))}ge||t.flags&512&&Bs(t)}catch(p){Z(t,t.return,p)}}if(t===e){T=null;break}if(n=t.sibling,n!==null){n.return=t.return,T=n;break}T=t.return}}function jc(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var n=t.sibling;if(n!==null){n.return=t.return,T=n;break}T=t.return}}function Cc(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Qo(4,t)}catch(l){Z(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){Z(t,i,l)}}var o=t.return;try{Bs(t)}catch(l){Z(t,o,l)}break;case 5:var a=t.return;try{Bs(t)}catch(l){Z(t,a,l)}}}catch(l){Z(t,t.return,l)}if(t===e){T=null;break}var s=t.sibling;if(s!==null){s.return=t.return,T=s;break}T=t.return}}var Kv=Math.ceil,Co=kt.ReactCurrentDispatcher,Ql=kt.ReactCurrentOwner,qe=kt.ReactCurrentBatchConfig,z=0,ce=null,re=null,pe=0,Le=0,Vn=Kt(0),oe=0,si=null,vn=0,Go=0,Gl=0,Ur=null,Pe=null,Xl=0,ir=1/0,pt=null,Po=!1,Vs=null,Ft=null,Fi=!1,At=null,To=0,Fr=0,Hs=null,io=-1,oo=0;function _e(){return z&6?ee():io!==-1?io:io=ee()}function Mt(e){return e.mode&1?z&2&&pe!==0?pe&-pe:Ov.transition!==null?(oo===0&&(oo=tp()),oo):(e=U,e!==0||(e=window.event,e=e===void 0?16:lp(e.type)),e):1}function tt(e,t,n,r){if(50<Fr)throw Fr=0,Hs=null,Error(E(185));fi(e,n,r),(!(z&2)||e!==ce)&&(e===ce&&(!(z&2)&&(Go|=n),oe===4&&Tt(e,pe)),Ae(e,r),n===1&&z===0&&!(t.mode&1)&&(ir=ee()+500,Ho&&Jt()))}function Ae(e,t){var n=e.callbackNode;Om(e,t);var r=po(e,e===ce?pe:0);if(r===0)n!==null&&$u(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&$u(n),t===1)e.tag===0?Tv(Pc.bind(null,e)):Cp(Pc.bind(null,e)),bv(function(){!(z&6)&&Jt()}),n=null;else{switch(np(r)){case 1:n=Sl;break;case 4:n=Zd;break;case 16:n=co;break;case 536870912:n=ep;break;default:n=co}n=jf(n,wf.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function wf(e,t){if(io=-1,oo=0,z&6)throw Error(E(327));var n=e.callbackNode;if(Xn()&&e.callbackNode!==n)return null;var r=po(e,e===ce?pe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Oo(e,r);else{t=r;var i=z;z|=2;var o=_f();(ce!==e||pe!==t)&&(pt=null,ir=ee()+500,dn(e,t));do try{Gv();break}catch(s){xf(e,s)}while(1);$l(),Co.current=o,z=i,re!==null?t=0:(ce=null,pe=0,t=oe)}if(t!==0){if(t===2&&(i=ws(e),i!==0&&(r=i,t=Ks(e,i))),t===1)throw n=si,dn(e,0),Tt(e,r),Ae(e,ee()),n;if(t===6)Tt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Jv(i)&&(t=Oo(e,r),t===2&&(o=ws(e),o!==0&&(r=o,t=Ks(e,o))),t===1))throw n=si,dn(e,0),Tt(e,r),Ae(e,ee()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(E(345));case 2:nn(e,Pe,pt);break;case 3:if(Tt(e,r),(r&130023424)===r&&(t=Xl+500-ee(),10<t)){if(po(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){_e(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Cs(nn.bind(null,e,Pe,pt),t);break}nn(e,Pe,pt);break;case 4:if(Tt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var a=31-et(r);o=1<<a,a=t[a],a>i&&(i=a),r&=~o}if(r=i,r=ee()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Kv(r/1960))-r,10<r){e.timeoutHandle=Cs(nn.bind(null,e,Pe,pt),r);break}nn(e,Pe,pt);break;case 5:nn(e,Pe,pt);break;default:throw Error(E(329))}}}return Ae(e,ee()),e.callbackNode===n?wf.bind(null,e):null}function Ks(e,t){var n=Ur;return e.current.memoizedState.isDehydrated&&(dn(e,t).flags|=256),e=Oo(e,t),e!==2&&(t=Pe,Pe=n,t!==null&&Js(t)),e}function Js(e){Pe===null?Pe=e:Pe.push.apply(Pe,e)}function Jv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!nt(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Tt(e,t){for(t&=~Gl,t&=~Go,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-et(t),r=1<<n;e[n]=-1,t&=~r}}function Pc(e){if(z&6)throw Error(E(327));Xn();var t=po(e,0);if(!(t&1))return Ae(e,ee()),null;var n=Oo(e,t);if(e.tag!==0&&n===2){var r=ws(e);r!==0&&(t=r,n=Ks(e,r))}if(n===1)throw n=si,dn(e,0),Tt(e,t),Ae(e,ee()),n;if(n===6)throw Error(E(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,nn(e,Pe,pt),Ae(e,ee()),null}function Yl(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(ir=ee()+500,Ho&&Jt())}}function gn(e){At!==null&&At.tag===0&&!(z&6)&&Xn();var t=z;z|=1;var n=qe.transition,r=U;try{if(qe.transition=null,U=1,e)return e()}finally{U=r,qe.transition=n,z=t,!(z&6)&&Jt()}}function Zl(){Le=Vn.current,V(Vn)}function dn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Ev(n)),re!==null)for(n=re.return;n!==null;){var r=n;switch(Al(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&go();break;case 3:nr(),V(Re),V(ye),Bl();break;case 5:Ml(r);break;case 4:nr();break;case 13:V(G);break;case 19:V(G);break;case 10:Nl(r.type._context);break;case 22:case 23:Zl()}n=n.return}if(ce=e,re=e=Bt(e.current,null),pe=Le=t,oe=0,si=null,Gl=Go=vn=0,Pe=Ur=null,ln!==null){for(t=0;t<ln.length;t++)if(n=ln[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var a=o.next;o.next=i,r.next=a}n.pending=r}ln=null}return e}function xf(e,t){do{var n=re;try{if($l(),to.current=jo,bo){for(var r=X.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}bo=!1}if(mn=0,ue=ie=X=null,Nr=!1,ii=0,Ql.current=null,n===null||n.return===null){oe=1,si=t,re=null;break}e:{var o=e,a=n.return,s=n,l=t;if(t=pe,s.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=s,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var p=c.alternate;p?(c.updateQueue=p.updateQueue,c.memoizedState=p.memoizedState,c.lanes=p.lanes):(c.updateQueue=null,c.memoizedState=null)}var v=mc(a);if(v!==null){v.flags&=-257,vc(v,a,s,o,t),v.mode&1&&hc(o,u,t),t=v,l=u;var g=t.updateQueue;if(g===null){var w=new Set;w.add(l),t.updateQueue=w}else g.add(l);break e}else{if(!(t&1)){hc(o,u,t),eu();break e}l=Error(E(426))}}else if(K&&s.mode&1){var _=mc(a);if(_!==null){!(_.flags&65536)&&(_.flags|=256),vc(_,a,s,o,t),Dl(rr(l,s));break e}}o=l=rr(l,s),oe!==4&&(oe=2),Ur===null?Ur=[o]:Ur.push(o),o=a;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=nf(o,l,t);lc(o,m);break e;case 1:s=l;var h=o.type,f=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Ft===null||!Ft.has(f)))){o.flags|=65536,t&=-t,o.lanes|=t;var y=rf(o,s,t);lc(o,y);break e}}o=o.return}while(o!==null)}Sf(n)}catch(S){t=S,re===n&&n!==null&&(re=n=n.return);continue}break}while(1)}function _f(){var e=Co.current;return Co.current=jo,e===null?jo:e}function eu(){(oe===0||oe===3||oe===2)&&(oe=4),ce===null||!(vn&268435455)&&!(Go&268435455)||Tt(ce,pe)}function Oo(e,t){var n=z;z|=2;var r=_f();(ce!==e||pe!==t)&&(pt=null,dn(e,t));do try{Qv();break}catch(i){xf(e,i)}while(1);if($l(),z=n,Co.current=r,re!==null)throw Error(E(261));return ce=null,pe=0,oe}function Qv(){for(;re!==null;)kf(re)}function Gv(){for(;re!==null&&!_m();)kf(re)}function kf(e){var t=bf(e.alternate,e,Le);e.memoizedProps=e.pendingProps,t===null?Sf(e):re=t,Ql.current=null}function Sf(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Wv(n,t),n!==null){n.flags&=32767,re=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{oe=6,re=null;return}}else if(n=Bv(n,t,Le),n!==null){re=n;return}if(t=t.sibling,t!==null){re=t;return}re=t=e}while(t!==null);oe===0&&(oe=5)}function nn(e,t,n){var r=U,i=qe.transition;try{qe.transition=null,U=1,Xv(e,t,n,r)}finally{qe.transition=i,U=r}return null}function Xv(e,t,n,r){do Xn();while(At!==null);if(z&6)throw Error(E(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(E(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Rm(e,o),e===ce&&(re=ce=null,pe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Fi||(Fi=!0,jf(co,function(){return Xn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=qe.transition,qe.transition=null;var a=U;U=1;var s=z;z|=4,Ql.current=null,Vv(e,n),gf(n,e),gv(bs),fo=!!Es,bs=Es=null,e.current=n,Hv(n),km(),z=s,U=a,qe.transition=o}else e.current=n;if(Fi&&(Fi=!1,At=e,To=i),o=e.pendingLanes,o===0&&(Ft=null),bm(n.stateNode),Ae(e,ee()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Po)throw Po=!1,e=Vs,Vs=null,e;return To&1&&e.tag!==0&&Xn(),o=e.pendingLanes,o&1?e===Hs?Fr++:(Fr=0,Hs=e):Fr=0,Jt(),null}function Xn(){if(At!==null){var e=np(To),t=qe.transition,n=U;try{if(qe.transition=null,U=16>e?16:e,At===null)var r=!1;else{if(e=At,At=null,To=0,z&6)throw Error(E(331));var i=z;for(z|=4,T=e.current;T!==null;){var o=T,a=o.child;if(T.flags&16){var s=o.deletions;if(s!==null){for(var l=0;l<s.length;l++){var u=s[l];for(T=u;T!==null;){var c=T;switch(c.tag){case 0:case 11:case 15:zr(8,c,o)}var d=c.child;if(d!==null)d.return=c,T=d;else for(;T!==null;){c=T;var p=c.sibling,v=c.return;if(hf(c),c===u){T=null;break}if(p!==null){p.return=v,T=p;break}T=v}}}var g=o.alternate;if(g!==null){var w=g.child;if(w!==null){g.child=null;do{var _=w.sibling;w.sibling=null,w=_}while(w!==null)}}T=o}}if(o.subtreeFlags&2064&&a!==null)a.return=o,T=a;else e:for(;T!==null;){if(o=T,o.flags&2048)switch(o.tag){case 0:case 11:case 15:zr(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,T=m;break e}T=o.return}}var h=e.current;for(T=h;T!==null;){a=T;var f=a.child;if(a.subtreeFlags&2064&&f!==null)f.return=a,T=f;else e:for(a=h;T!==null;){if(s=T,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:Qo(9,s)}}catch(S){Z(s,s.return,S)}if(s===a){T=null;break e}var y=s.sibling;if(y!==null){y.return=s.return,T=y;break e}T=s.return}}if(z=i,Jt(),lt&&typeof lt.onPostCommitFiberRoot=="function")try{lt.onPostCommitFiberRoot(Mo,e)}catch{}r=!0}return r}finally{U=n,qe.transition=t}}return!1}function Tc(e,t,n){t=rr(n,t),t=nf(e,t,1),e=Ut(e,t,1),t=_e(),e!==null&&(fi(e,1,t),Ae(e,t))}function Z(e,t,n){if(e.tag===3)Tc(e,e,n);else for(;t!==null;){if(t.tag===3){Tc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ft===null||!Ft.has(r))){e=rr(n,e),e=rf(t,e,1),t=Ut(t,e,1),e=_e(),t!==null&&(fi(t,1,e),Ae(t,e));break}}t=t.return}}function Yv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=_e(),e.pingedLanes|=e.suspendedLanes&n,ce===e&&(pe&n)===n&&(oe===4||oe===3&&(pe&130023424)===pe&&500>ee()-Xl?dn(e,0):Gl|=n),Ae(e,t)}function Ef(e,t){t===0&&(e.mode&1?(t=Oi,Oi<<=1,!(Oi&130023424)&&(Oi=4194304)):t=1);var n=_e();e=xt(e,t),e!==null&&(fi(e,t,n),Ae(e,n))}function Zv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ef(e,n)}function eg(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(E(314))}r!==null&&r.delete(t),Ef(e,n)}var bf;bf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Re.current)Oe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Oe=!1,Mv(e,t,n);Oe=!!(e.flags&131072)}else Oe=!1,K&&t.flags&1048576&&Pp(t,xo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ro(e,t),e=t.pendingProps;var i=Zn(t,ye.current);Gn(t,n),i=ql(null,t,r,e,i,n);var o=Vl();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ie(r)?(o=!0,yo(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Ul(t),i.updater=Jo,t.stateNode=i,i._reactInternals=t,Ds(t,r,e,n),t=Ns(null,t,r,!0,o,n)):(t.tag=0,K&&o&&Il(t),xe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ro(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=ng(r),e=Ge(r,e),i){case 0:t=$s(null,t,r,e,n);break e;case 1:t=wc(null,t,r,e,n);break e;case 11:t=gc(null,t,r,e,n);break e;case 14:t=yc(null,t,r,Ge(r.type,e),n);break e}throw Error(E(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),$s(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),wc(e,t,r,i,n);case 3:e:{if(lf(t),e===null)throw Error(E(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Dp(e,t),So(t,r,null,n);var a=t.memoizedState;if(r=a.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=rr(Error(E(423)),t),t=xc(e,t,r,n,i);break e}else if(r!==i){i=rr(Error(E(424)),t),t=xc(e,t,r,n,i);break e}else for($e=zt(t.stateNode.containerInfo.firstChild),Ne=t,K=!0,Ze=null,n=Ip(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(er(),r===i){t=_t(e,t,n);break e}xe(e,t,r,n)}t=t.child}return t;case 5:return Lp(t),e===null&&Rs(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,a=i.children,js(r,i)?a=null:o!==null&&js(r,o)&&(t.flags|=32),sf(e,t),xe(e,t,a,n),t.child;case 6:return e===null&&Rs(t),null;case 13:return uf(e,t,n);case 4:return Fl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=tr(t,null,r,n):xe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),gc(e,t,r,i,n);case 7:return xe(e,t,t.pendingProps,n),t.child;case 8:return xe(e,t,t.pendingProps.children,n),t.child;case 12:return xe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,a=i.value,W(_o,r._currentValue),r._currentValue=a,o!==null)if(nt(o.value,a)){if(o.children===i.children&&!Re.current){t=_t(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var s=o.dependencies;if(s!==null){a=o.child;for(var l=s.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=gt(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Is(o.return,n,t),s.lanes|=n;break}l=l.next}}else if(o.tag===10)a=o.type===t.type?null:o.child;else if(o.tag===18){if(a=o.return,a===null)throw Error(E(341));a.lanes|=n,s=a.alternate,s!==null&&(s.lanes|=n),Is(a,n,t),a=o.sibling}else a=o.child;if(a!==null)a.return=o;else for(a=o;a!==null;){if(a===t){a=null;break}if(o=a.sibling,o!==null){o.return=a.return,a=o;break}a=a.return}o=a}xe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Gn(t,n),i=He(i),r=r(i),t.flags|=1,xe(e,t,r,n),t.child;case 14:return r=t.type,i=Ge(r,t.pendingProps),i=Ge(r.type,i),yc(e,t,r,i,n);case 15:return of(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),ro(e,t),t.tag=1,Ie(r)?(e=!0,yo(t)):e=!1,Gn(t,n),tf(t,r,i),Ds(t,r,i,n),Ns(null,t,r,!0,e,n);case 19:return cf(e,t,n);case 22:return af(e,t,n)}throw Error(E(156,t.tag))};function jf(e,t){return Yd(e,t)}function tg(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function We(e,t,n,r){return new tg(e,t,n,r)}function tu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ng(e){if(typeof e=="function")return tu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===xl)return 11;if(e===_l)return 14}return 2}function Bt(e,t){var n=e.alternate;return n===null?(n=We(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ao(e,t,n,r,i,o){var a=2;if(r=e,typeof e=="function")tu(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case Ln:return pn(n.children,i,o,t);case wl:a=8,i|=8;break;case is:return e=We(12,n,t,i|2),e.elementType=is,e.lanes=o,e;case os:return e=We(13,n,t,i),e.elementType=os,e.lanes=o,e;case as:return e=We(19,n,t,i),e.elementType=as,e.lanes=o,e;case Ld:return Xo(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ad:a=10;break e;case Dd:a=9;break e;case xl:a=11;break e;case _l:a=14;break e;case bt:a=16,r=null;break e}throw Error(E(130,e==null?e:typeof e,""))}return t=We(a,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function pn(e,t,n,r){return e=We(7,e,r,t),e.lanes=n,e}function Xo(e,t,n,r){return e=We(22,e,r,t),e.elementType=Ld,e.lanes=n,e.stateNode={isHidden:!1},e}function qa(e,t,n){return e=We(6,e,null,t),e.lanes=n,e}function Va(e,t,n){return t=We(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function rg(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ba(0),this.expirationTimes=ba(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ba(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function nu(e,t,n,r,i,o,a,s,l){return e=new rg(e,t,n,s,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=We(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ul(o),e}function ig(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Dn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Cf(e){if(!e)return Vt;e=e._reactInternals;e:{if(wn(e)!==e||e.tag!==1)throw Error(E(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ie(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(E(171))}if(e.tag===1){var n=e.type;if(Ie(n))return jp(e,n,t)}return t}function Pf(e,t,n,r,i,o,a,s,l){return e=nu(n,r,!0,e,i,o,a,s,l),e.context=Cf(null),n=e.current,r=_e(),i=Mt(n),o=gt(r,i),o.callback=t??null,Ut(n,o,i),e.current.lanes=i,fi(e,i,r),Ae(e,r),e}function Yo(e,t,n,r){var i=t.current,o=_e(),a=Mt(i);return n=Cf(n),t.context===null?t.context=n:t.pendingContext=n,t=gt(o,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ut(i,t,a),e!==null&&(tt(e,i,a,o),eo(e,i,a)),a}function Ro(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Oc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ru(e,t){Oc(e,t),(e=e.alternate)&&Oc(e,t)}function og(){return null}var Tf=typeof reportError=="function"?reportError:function(e){console.error(e)};function iu(e){this._internalRoot=e}Zo.prototype.render=iu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(E(409));Yo(e,t,null,null)};Zo.prototype.unmount=iu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;gn(function(){Yo(null,e,null,null)}),t[wt]=null}};function Zo(e){this._internalRoot=e}Zo.prototype.unstable_scheduleHydration=function(e){if(e){var t=op();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Pt.length&&t!==0&&t<Pt[n].priority;n++);Pt.splice(n,0,e),n===0&&sp(e)}};function ou(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ea(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Rc(){}function ag(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=Ro(a);o.call(u)}}var a=Pf(t,r,e,0,null,!1,!1,"",Rc);return e._reactRootContainer=a,e[wt]=a.current,Zr(e.nodeType===8?e.parentNode:e),gn(),a}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var s=r;r=function(){var u=Ro(l);s.call(u)}}var l=nu(e,0,!1,null,null,!1,!1,"",Rc);return e._reactRootContainer=l,e[wt]=l.current,Zr(e.nodeType===8?e.parentNode:e),gn(function(){Yo(t,l,n,r)}),l}function ta(e,t,n,r,i){var o=n._reactRootContainer;if(o){var a=o;if(typeof i=="function"){var s=i;i=function(){var l=Ro(a);s.call(l)}}Yo(t,a,e,i)}else a=ag(n,t,e,i,r);return Ro(a)}rp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Tr(t.pendingLanes);n!==0&&(El(t,n|1),Ae(t,ee()),!(z&6)&&(ir=ee()+500,Jt()))}break;case 13:gn(function(){var r=xt(e,1);if(r!==null){var i=_e();tt(r,e,1,i)}}),ru(e,1)}};bl=function(e){if(e.tag===13){var t=xt(e,134217728);if(t!==null){var n=_e();tt(t,e,134217728,n)}ru(e,134217728)}};ip=function(e){if(e.tag===13){var t=Mt(e),n=xt(e,t);if(n!==null){var r=_e();tt(n,e,t,r)}ru(e,t)}};op=function(){return U};ap=function(e,t){var n=U;try{return U=e,t()}finally{U=n}};vs=function(e,t,n){switch(t){case"input":if(us(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Vo(r);if(!i)throw Error(E(90));Nd(r),us(r,i)}}}break;case"textarea":Ud(e,n);break;case"select":t=n.value,t!=null&&Hn(e,!!n.multiple,t,!1)}};Hd=Yl;Kd=gn;var sg={usingClientEntryPoint:!1,Events:[mi,Un,Vo,qd,Vd,Yl]},br={findFiberByHostInstance:sn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},lg={bundleType:br.bundleType,version:br.version,rendererPackageName:br.rendererPackageName,rendererConfig:br.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:kt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Gd(e),e===null?null:e.stateNode},findFiberByHostInstance:br.findFiberByHostInstance||og,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Mi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Mi.isDisabled&&Mi.supportsFiber)try{Mo=Mi.inject(lg),lt=Mi}catch{}}Ue.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=sg;Ue.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ou(t))throw Error(E(200));return ig(e,t,null,n)};Ue.createRoot=function(e,t){if(!ou(e))throw Error(E(299));var n=!1,r="",i=Tf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=nu(e,1,!1,null,null,n,!1,r,i),e[wt]=t.current,Zr(e.nodeType===8?e.parentNode:e),new iu(t)};Ue.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(E(188)):(e=Object.keys(e).join(","),Error(E(268,e)));return e=Gd(t),e=e===null?null:e.stateNode,e};Ue.flushSync=function(e){return gn(e)};Ue.hydrate=function(e,t,n){if(!ea(t))throw Error(E(200));return ta(null,e,t,!0,n)};Ue.hydrateRoot=function(e,t,n){if(!ou(e))throw Error(E(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",a=Tf;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Pf(t,null,e,1,n??null,i,!1,o,a),e[wt]=t.current,Zr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Zo(t)};Ue.render=function(e,t,n){if(!ea(t))throw Error(E(200));return ta(null,e,t,!1,n)};Ue.unmountComponentAtNode=function(e){if(!ea(e))throw Error(E(40));return e._reactRootContainer?(gn(function(){ta(null,null,e,!1,function(){e._reactRootContainer=null,e[wt]=null})}),!0):!1};Ue.unstable_batchedUpdates=Yl;Ue.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ea(n))throw Error(E(200));if(e==null||e._reactInternals===void 0)throw Error(E(38));return ta(e,t,n,!1,r)};Ue.version="18.3.1-next-f1338f8080-20240426";function Of(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Of)}catch(e){console.error(e)}}Of(),Td.exports=Ue;var ug=Td.exports,Ic=ug;ns.createRoot=Ic.createRoot,ns.hydrateRoot=Ic.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function li(){return li=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},li.apply(this,arguments)}var Dt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Dt||(Dt={}));const Ac="popstate";function cg(e){e===void 0&&(e={});function t(r,i){let{pathname:o,search:a,hash:s}=r.location;return Qs("",{pathname:o,search:a,hash:s},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Io(i)}return pg(t,n,null,e)}function te(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Rf(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function dg(){return Math.random().toString(36).substr(2,8)}function Dc(e,t){return{usr:e.state,key:e.key,idx:t}}function Qs(e,t,n,r){return n===void 0&&(n=null),li({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?dr(t):t,{state:n,key:t&&t.key||r||dg()})}function Io(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function dr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function pg(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:o=!1}=r,a=i.history,s=Dt.Pop,l=null,u=c();u==null&&(u=0,a.replaceState(li({},a.state,{idx:u}),""));function c(){return(a.state||{idx:null}).idx}function d(){s=Dt.Pop;let _=c(),m=_==null?null:_-u;u=_,l&&l({action:s,location:w.location,delta:m})}function p(_,m){s=Dt.Push;let h=Qs(w.location,_,m);n&&n(h,_),u=c()+1;let f=Dc(h,u),y=w.createHref(h);try{a.pushState(f,"",y)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;i.location.assign(y)}o&&l&&l({action:s,location:w.location,delta:1})}function v(_,m){s=Dt.Replace;let h=Qs(w.location,_,m);n&&n(h,_),u=c();let f=Dc(h,u),y=w.createHref(h);a.replaceState(f,"",y),o&&l&&l({action:s,location:w.location,delta:0})}function g(_){let m=i.location.origin!=="null"?i.location.origin:i.location.href,h=typeof _=="string"?_:Io(_);return h=h.replace(/ $/,"%20"),te(m,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,m)}let w={get action(){return s},get location(){return e(i,a)},listen(_){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(Ac,d),l=_,()=>{i.removeEventListener(Ac,d),l=null}},createHref(_){return t(i,_)},createURL:g,encodeLocation(_){let m=g(_);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:p,replace:v,go(_){return a.go(_)}};return w}var Lc;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Lc||(Lc={}));function fg(e,t,n){return n===void 0&&(n="/"),hg(e,t,n,!1)}function hg(e,t,n,r){let i=typeof t=="string"?dr(t):t,o=au(i.pathname||"/",n);if(o==null)return null;let a=If(e);mg(a);let s=null;for(let l=0;s==null&&l<a.length;++l){let u=jg(o);s=Eg(a[l],u,r)}return s}function If(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(o,a,s)=>{let l={relativePath:s===void 0?o.path||"":s,caseSensitive:o.caseSensitive===!0,childrenIndex:a,route:o};l.relativePath.startsWith("/")&&(te(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=Wt([r,l.relativePath]),c=n.concat(l);o.children&&o.children.length>0&&(te(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),If(o.children,t,c,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:kg(u,o.index),routesMeta:c})};return e.forEach((o,a)=>{var s;if(o.path===""||!((s=o.path)!=null&&s.includes("?")))i(o,a);else for(let l of Af(o.path))i(o,a,l)}),t}function Af(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return i?[o,""]:[o];let a=Af(r.join("/")),s=[];return s.push(...a.map(l=>l===""?o:[o,l].join("/"))),i&&s.push(...a),s.map(l=>e.startsWith("/")&&l===""?"/":l)}function mg(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Sg(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const vg=/^:[\w-]+$/,gg=3,yg=2,wg=1,xg=10,_g=-2,$c=e=>e==="*";function kg(e,t){let n=e.split("/"),r=n.length;return n.some($c)&&(r+=_g),t&&(r+=yg),n.filter(i=>!$c(i)).reduce((i,o)=>i+(vg.test(o)?gg:o===""?wg:xg),r)}function Sg(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function Eg(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,i={},o="/",a=[];for(let s=0;s<r.length;++s){let l=r[s],u=s===r.length-1,c=o==="/"?t:t.slice(o.length)||"/",d=Nc({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),p=l.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=Nc({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!d)return null;Object.assign(i,d.params),a.push({params:i,pathname:Wt([o,d.pathname]),pathnameBase:Og(Wt([o,d.pathnameBase])),route:p}),d.pathnameBase!=="/"&&(o=Wt([o,d.pathnameBase]))}return a}function Nc(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=bg(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let o=i[0],a=o.replace(/(.)\/+$/,"$1"),s=i.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:p,isOptional:v}=c;if(p==="*"){let w=s[d]||"";a=o.slice(0,o.length-w.length).replace(/(.)\/+$/,"$1")}const g=s[d];return v&&!g?u[p]=void 0:u[p]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:a,pattern:e}}function bg(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Rf(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,s,l)=>(r.push({paramName:s,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function jg(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Rf(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function au(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Cg(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?dr(e):e;return{pathname:n?n.startsWith("/")?n:Pg(n,t):t,search:Rg(r),hash:Ig(i)}}function Pg(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Ha(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Tg(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function su(e,t){let n=Tg(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function lu(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=dr(e):(i=li({},e),te(!i.pathname||!i.pathname.includes("?"),Ha("?","pathname","search",i)),te(!i.pathname||!i.pathname.includes("#"),Ha("#","pathname","hash",i)),te(!i.search||!i.search.includes("#"),Ha("#","search","hash",i)));let o=e===""||i.pathname==="",a=o?"/":i.pathname,s;if(a==null)s=n;else{let d=t.length-1;if(!r&&a.startsWith("..")){let p=a.split("/");for(;p[0]==="..";)p.shift(),d-=1;i.pathname=p.join("/")}s=d>=0?t[d]:"/"}let l=Cg(i,s),u=a&&a!=="/"&&a.endsWith("/"),c=(o||a===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const Wt=e=>e.join("/").replace(/\/\/+/g,"/"),Og=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Rg=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ig=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ag(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Df=["post","put","patch","delete"];new Set(Df);const Dg=["get",...Df];new Set(Dg);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ui(){return ui=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ui.apply(this,arguments)}const uu=k.createContext(null),Lg=k.createContext(null),Qt=k.createContext(null),na=k.createContext(null),Gt=k.createContext({outlet:null,matches:[],isDataRoute:!1}),Lf=k.createContext(null);function $g(e,t){let{relative:n}=t===void 0?{}:t;pr()||te(!1);let{basename:r,navigator:i}=k.useContext(Qt),{hash:o,pathname:a,search:s}=Nf(e,{relative:n}),l=a;return r!=="/"&&(l=a==="/"?r:Wt([r,a])),i.createHref({pathname:l,search:s,hash:o})}function pr(){return k.useContext(na)!=null}function fr(){return pr()||te(!1),k.useContext(na).location}function $f(e){k.useContext(Qt).static||k.useLayoutEffect(e)}function ra(){let{isDataRoute:e}=k.useContext(Gt);return e?Qg():Ng()}function Ng(){pr()||te(!1);let e=k.useContext(uu),{basename:t,future:n,navigator:r}=k.useContext(Qt),{matches:i}=k.useContext(Gt),{pathname:o}=fr(),a=JSON.stringify(su(i,n.v7_relativeSplatPath)),s=k.useRef(!1);return $f(()=>{s.current=!0}),k.useCallback(function(u,c){if(c===void 0&&(c={}),!s.current)return;if(typeof u=="number"){r.go(u);return}let d=lu(u,JSON.parse(a),o,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Wt([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,a,o,e])}function Nf(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=k.useContext(Qt),{matches:i}=k.useContext(Gt),{pathname:o}=fr(),a=JSON.stringify(su(i,r.v7_relativeSplatPath));return k.useMemo(()=>lu(e,JSON.parse(a),o,n==="path"),[e,a,o,n])}function zg(e,t){return Ug(e,t)}function Ug(e,t,n,r){pr()||te(!1);let{navigator:i}=k.useContext(Qt),{matches:o}=k.useContext(Gt),a=o[o.length-1],s=a?a.params:{};a&&a.pathname;let l=a?a.pathnameBase:"/";a&&a.route;let u=fr(),c;if(t){var d;let _=typeof t=="string"?dr(t):t;l==="/"||(d=_.pathname)!=null&&d.startsWith(l)||te(!1),c=_}else c=u;let p=c.pathname||"/",v=p;if(l!=="/"){let _=l.replace(/^\//,"").split("/");v="/"+p.replace(/^\//,"").split("/").slice(_.length).join("/")}let g=fg(e,{pathname:v}),w=qg(g&&g.map(_=>Object.assign({},_,{params:Object.assign({},s,_.params),pathname:Wt([l,i.encodeLocation?i.encodeLocation(_.pathname).pathname:_.pathname]),pathnameBase:_.pathnameBase==="/"?l:Wt([l,i.encodeLocation?i.encodeLocation(_.pathnameBase).pathname:_.pathnameBase])})),o,n,r);return t&&w?k.createElement(na.Provider,{value:{location:ui({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:Dt.Pop}},w):w}function Fg(){let e=Jg(),t=Ag(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},o=null;return k.createElement(k.Fragment,null,k.createElement("h2",null,"Unexpected Application Error!"),k.createElement("h3",{style:{fontStyle:"italic"}},t),n?k.createElement("pre",{style:i},n):null,o)}const Mg=k.createElement(Fg,null);class Bg extends k.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?k.createElement(Gt.Provider,{value:this.props.routeContext},k.createElement(Lf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Wg(e){let{routeContext:t,match:n,children:r}=e,i=k.useContext(uu);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),k.createElement(Gt.Provider,{value:t},r)}function qg(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let a=e,s=(i=n)==null?void 0:i.errors;if(s!=null){let c=a.findIndex(d=>d.route.id&&(s==null?void 0:s[d.route.id])!==void 0);c>=0||te(!1),a=a.slice(0,Math.min(a.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<a.length;c++){let d=a[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:p,errors:v}=n,g=d.route.loader&&p[d.route.id]===void 0&&(!v||v[d.route.id]===void 0);if(d.route.lazy||g){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((c,d,p)=>{let v,g=!1,w=null,_=null;n&&(v=s&&d.route.id?s[d.route.id]:void 0,w=d.route.errorElement||Mg,l&&(u<0&&p===0?(Gg("route-fallback",!1),g=!0,_=null):u===p&&(g=!0,_=d.route.hydrateFallbackElement||null)));let m=t.concat(a.slice(0,p+1)),h=()=>{let f;return v?f=w:g?f=_:d.route.Component?f=k.createElement(d.route.Component,null):d.route.element?f=d.route.element:f=c,k.createElement(Wg,{match:d,routeContext:{outlet:c,matches:m,isDataRoute:n!=null},children:f})};return n&&(d.route.ErrorBoundary||d.route.errorElement||p===0)?k.createElement(Bg,{location:n.location,revalidation:n.revalidation,component:w,error:v,children:h(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):h()},null)}var zf=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(zf||{}),Ao=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ao||{});function Vg(e){let t=k.useContext(uu);return t||te(!1),t}function Hg(e){let t=k.useContext(Lg);return t||te(!1),t}function Kg(e){let t=k.useContext(Gt);return t||te(!1),t}function Uf(e){let t=Kg(),n=t.matches[t.matches.length-1];return n.route.id||te(!1),n.route.id}function Jg(){var e;let t=k.useContext(Lf),n=Hg(Ao.UseRouteError),r=Uf(Ao.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Qg(){let{router:e}=Vg(zf.UseNavigateStable),t=Uf(Ao.UseNavigateStable),n=k.useRef(!1);return $f(()=>{n.current=!0}),k.useCallback(function(i,o){o===void 0&&(o={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,ui({fromRouteId:t},o)))},[e,t])}const zc={};function Gg(e,t,n){!t&&!zc[e]&&(zc[e]=!0)}function Xg(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Ff(e){let{to:t,replace:n,state:r,relative:i}=e;pr()||te(!1);let{future:o,static:a}=k.useContext(Qt),{matches:s}=k.useContext(Gt),{pathname:l}=fr(),u=ra(),c=lu(t,su(s,o.v7_relativeSplatPath),l,i==="path"),d=JSON.stringify(c);return k.useEffect(()=>u(JSON.parse(d),{replace:n,state:r,relative:i}),[u,d,i,n,r]),null}function In(e){te(!1)}function Yg(e){let{basename:t="/",children:n=null,location:r,navigationType:i=Dt.Pop,navigator:o,static:a=!1,future:s}=e;pr()&&te(!1);let l=t.replace(/^\/*/,"/"),u=k.useMemo(()=>({basename:l,navigator:o,static:a,future:ui({v7_relativeSplatPath:!1},s)}),[l,s,o,a]);typeof r=="string"&&(r=dr(r));let{pathname:c="/",search:d="",hash:p="",state:v=null,key:g="default"}=r,w=k.useMemo(()=>{let _=au(c,l);return _==null?null:{location:{pathname:_,search:d,hash:p,state:v,key:g},navigationType:i}},[l,c,d,p,v,g,i]);return w==null?null:k.createElement(Qt.Provider,{value:u},k.createElement(na.Provider,{children:n,value:w}))}function Zg(e){let{children:t,location:n}=e;return zg(Gs(t),n)}new Promise(()=>{});function Gs(e,t){t===void 0&&(t=[]);let n=[];return k.Children.forEach(e,(r,i)=>{if(!k.isValidElement(r))return;let o=[...t,i];if(r.type===k.Fragment){n.push.apply(n,Gs(r.props.children,o));return}r.type!==In&&te(!1),!r.props.index||!r.props.children||te(!1);let a={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=Gs(r.props.children,o)),n.push(a)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Xs(){return Xs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xs.apply(this,arguments)}function ey(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function ty(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ny(e,t){return e.button===0&&(!t||t==="_self")&&!ty(e)}const ry=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],iy="6";try{window.__reactRouterVersion=iy}catch{}const oy="startTransition",Uc=Yh[oy];function ay(e){let{basename:t,children:n,future:r,window:i}=e,o=k.useRef();o.current==null&&(o.current=cg({window:i,v5Compat:!0}));let a=o.current,[s,l]=k.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},c=k.useCallback(d=>{u&&Uc?Uc(()=>l(d)):l(d)},[l,u]);return k.useLayoutEffect(()=>a.listen(c),[a,c]),k.useEffect(()=>Xg(r),[r]),k.createElement(Yg,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:a,future:r})}const sy=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ly=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Mf=k.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:o,replace:a,state:s,target:l,to:u,preventScrollReset:c,viewTransition:d}=t,p=ey(t,ry),{basename:v}=k.useContext(Qt),g,w=!1;if(typeof u=="string"&&ly.test(u)&&(g=u,sy))try{let f=new URL(window.location.href),y=u.startsWith("//")?new URL(f.protocol+u):new URL(u),S=au(y.pathname,v);y.origin===f.origin&&S!=null?u=S+y.search+y.hash:w=!0}catch{}let _=$g(u,{relative:i}),m=uy(u,{replace:a,state:s,target:l,preventScrollReset:c,relative:i,viewTransition:d});function h(f){r&&r(f),f.defaultPrevented||m(f)}return k.createElement("a",Xs({},p,{href:g||_,onClick:w||o?r:h,ref:n,target:l}))});var Fc;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Fc||(Fc={}));var Mc;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Mc||(Mc={}));function uy(e,t){let{target:n,replace:r,state:i,preventScrollReset:o,relative:a,viewTransition:s}=t===void 0?{}:t,l=ra(),u=fr(),c=Nf(e,{relative:a});return k.useCallback(d=>{if(ny(d,n)){d.preventDefault();let p=r!==void 0?r:Io(u)===Io(c);l(e,{replace:p,state:i,preventScrollReset:o,relative:a,viewTransition:s})}},[u,l,c,r,i,n,e,o,a,s])}const Bc=e=>{let t;const n=new Set,r=(c,d)=>{const p=typeof c=="function"?c(t):c;if(!Object.is(p,t)){const v=t;t=d??(typeof p!="object"||p===null)?p:Object.assign({},t,p),n.forEach(g=>g(t,v))}},i=()=>t,l={setState:r,getState:i,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{n.clear()}},u=t=e(r,i,l);return l},cy=e=>e?Bc(e):Bc;var Bf={exports:{}},Wf={},qf={exports:{}},Vf={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var or=k;function dy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var py=typeof Object.is=="function"?Object.is:dy,fy=or.useState,hy=or.useEffect,my=or.useLayoutEffect,vy=or.useDebugValue;function gy(e,t){var n=t(),r=fy({inst:{value:n,getSnapshot:t}}),i=r[0].inst,o=r[1];return my(function(){i.value=n,i.getSnapshot=t,Ka(i)&&o({inst:i})},[e,n,t]),hy(function(){return Ka(i)&&o({inst:i}),e(function(){Ka(i)&&o({inst:i})})},[e]),vy(n),n}function Ka(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!py(e,n)}catch{return!0}}function yy(e,t){return t()}var wy=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?yy:gy;Vf.useSyncExternalStore=or.useSyncExternalStore!==void 0?or.useSyncExternalStore:wy;qf.exports=Vf;var xy=qf.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ia=k,_y=xy;function ky(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Sy=typeof Object.is=="function"?Object.is:ky,Ey=_y.useSyncExternalStore,by=ia.useRef,jy=ia.useEffect,Cy=ia.useMemo,Py=ia.useDebugValue;Wf.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var o=by(null);if(o.current===null){var a={hasValue:!1,value:null};o.current=a}else a=o.current;o=Cy(function(){function l(v){if(!u){if(u=!0,c=v,v=r(v),i!==void 0&&a.hasValue){var g=a.value;if(i(g,v))return d=g}return d=v}if(g=d,Sy(c,v))return g;var w=r(v);return i!==void 0&&i(g,w)?(c=v,g):(c=v,d=w)}var u=!1,c,d,p=n===void 0?null:n;return[function(){return l(t())},p===null?void 0:function(){return l(p())}]},[t,n,r,i]);var s=Ey(e,o[0],o[1]);return jy(function(){a.hasValue=!0,a.value=s},[s]),Py(s),s};Bf.exports=Wf;var Ty=Bf.exports;const Oy=dl(Ty),{useDebugValue:Ry}=Fo,{useSyncExternalStoreWithSelector:Iy}=Oy;const Ay=e=>e;function Dy(e,t=Ay,n){const r=Iy(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return Ry(r),r}const Wc=e=>{const t=typeof e=="function"?cy(e):e,n=(r,i)=>Dy(t,r,i);return Object.assign(n,t),n},Hf=e=>e?Wc(e):Wc,Ly="modulepreload",$y=function(e){return"/"+e},qc={},gi=function(t,n,r){if(!n||n.length===0)return t();const i=document.getElementsByTagName("link");return Promise.all(n.map(o=>{if(o=$y(o),o in qc)return;qc[o]=!0;const a=o.endsWith(".css"),s=a?'[rel="stylesheet"]':"";if(!!r)for(let c=i.length-1;c>=0;c--){const d=i[c];if(d.href===o&&(!a||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${s}`))return;const u=document.createElement("link");if(u.rel=a?"stylesheet":Ly,a||(u.as="script",u.crossOrigin=""),u.href=o,document.head.appendChild(u),a)return new Promise((c,d)=>{u.addEventListener("load",c),u.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>t()).catch(o=>{const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o})},Ny=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>gi(()=>Promise.resolve().then(()=>hr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class cu extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class zy extends cu{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class Uy extends cu{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class Fy extends cu{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var Ys;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(Ys||(Ys={}));var My=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};class By{constructor(t,{headers:n={},customFetch:r,region:i=Ys.Any}={}){this.url=t,this.headers=n,this.region=i,this.fetch=Ny(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return My(this,void 0,void 0,function*(){try{const{headers:i,method:o,body:a}=n;let s={},{region:l}=n;l||(l=this.region),l&&l!=="any"&&(s["x-region"]=l);let u;a&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&(typeof Blob<"u"&&a instanceof Blob||a instanceof ArrayBuffer?(s["Content-Type"]="application/octet-stream",u=a):typeof a=="string"?(s["Content-Type"]="text/plain",u=a):typeof FormData<"u"&&a instanceof FormData?u=a:(s["Content-Type"]="application/json",u=JSON.stringify(a)));const c=yield this.fetch(`${this.url}/${t}`,{method:o||"POST",headers:Object.assign(Object.assign(Object.assign({},s),this.headers),i),body:u}).catch(g=>{throw new zy(g)}),d=c.headers.get("x-relay-error");if(d&&d==="true")throw new Uy(c);if(!c.ok)throw new Fy(c);let p=((r=c.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),v;return p==="application/json"?v=yield c.json():p==="application/octet-stream"?v=yield c.blob():p==="text/event-stream"?v=c:p==="multipart/form-data"?v=yield c.formData():v=yield c.text(),{data:v,error:null}}catch(i){return{data:null,error:i}}})}}var Te={},du={},oa={},yi={},aa={},sa={},Wy=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},ar=Wy();const qy=ar.fetch,Kf=ar.fetch.bind(ar),Jf=ar.Headers,Vy=ar.Request,Hy=ar.Response,hr=Object.freeze(Object.defineProperty({__proto__:null,Headers:Jf,Request:Vy,Response:Hy,default:Kf,fetch:qy},Symbol.toStringTag,{value:"Module"})),Ky=$h(hr);var la={};Object.defineProperty(la,"__esModule",{value:!0});let Jy=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};la.default=Jy;var Qf=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(sa,"__esModule",{value:!0});const Qy=Qf(Ky),Gy=Qf(la);let Xy=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=Qy.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=Object.assign({},this.headers),this.headers[t]=n,this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let i=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async o=>{var a,s,l;let u=null,c=null,d=null,p=o.status,v=o.statusText;if(o.ok){if(this.method!=="HEAD"){const m=await o.text();m===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?c=m:c=JSON.parse(m))}const w=(a=this.headers.Prefer)===null||a===void 0?void 0:a.match(/count=(exact|planned|estimated)/),_=(s=o.headers.get("content-range"))===null||s===void 0?void 0:s.split("/");w&&_&&_.length>1&&(d=parseInt(_[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(c)&&(c.length>1?(u={code:"PGRST116",details:`Results contain ${c.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},c=null,d=null,p=406,v="Not Acceptable"):c.length===1?c=c[0]:c=null)}else{const w=await o.text();try{u=JSON.parse(w),Array.isArray(u)&&o.status===404&&(c=[],u=null,p=200,v="OK")}catch{o.status===404&&w===""?(p=204,v="No Content"):u={message:w}}if(u&&this.isMaybeSingle&&(!((l=u==null?void 0:u.details)===null||l===void 0)&&l.includes("0 rows"))&&(u=null,p=200,v="OK"),u&&this.shouldThrowOnError)throw new Gy.default(u)}return{error:u,data:c,count:d,status:p,statusText:v}});return this.shouldThrowOnError||(i=i.catch(o=>{var a,s,l;return{error:{message:`${(a=o==null?void 0:o.name)!==null&&a!==void 0?a:"FetchError"}: ${o==null?void 0:o.message}`,details:`${(s=o==null?void 0:o.stack)!==null&&s!==void 0?s:""}`,hint:"",code:`${(l=o==null?void 0:o.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),i.then(t,n)}returns(){return this}overrideTypes(){return this}};sa.default=Xy;var Yy=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(aa,"__esModule",{value:!0});const Zy=Yy(sa);let e0=class extends Zy.default{select(t){let n=!1;const r=(t??"*").split("").map(i=>/\s/.test(i)&&!n?"":(i==='"'&&(n=!n),i)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:i,referencedTable:o=i}={}){const a=o?`${o}.order`:"order",s=this.url.searchParams.get(a);return this.url.searchParams.set(a,`${s?`${s},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const i=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:i=r}={}){const o=typeof i>"u"?"offset":`${i}.offset`,a=typeof i>"u"?"limit":`${i}.limit`;return this.url.searchParams.set(o,`${t}`),this.url.searchParams.set(a,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:i=!1,wal:o=!1,format:a="text"}={}){var s;const l=[t?"analyze":null,n?"verbose":null,r?"settings":null,i?"buffers":null,o?"wal":null].filter(Boolean).join("|"),u=(s=this.headers.Accept)!==null&&s!==void 0?s:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${a}; for="${u}"; options=${l};`,a==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};aa.default=e0;var t0=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(yi,"__esModule",{value:!0});const n0=t0(aa);let r0=class extends n0.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(i=>typeof i=="string"&&new RegExp("[,()]").test(i)?`"${i}"`:`${i}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:i}={}){let o="";i==="plain"?o="pl":i==="phrase"?o="ph":i==="websearch"&&(o="w");const a=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${o}fts${a}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};yi.default=r0;var i0=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(oa,"__esModule",{value:!0});const jr=i0(yi);let o0=class{constructor(t,{headers:n={},schema:r,fetch:i}){this.url=t,this.headers=n,this.schema=r,this.fetch=i}select(t,{head:n=!1,count:r}={}){const i=n?"HEAD":"GET";let o=!1;const a=(t??"*").split("").map(s=>/\s/.test(s)&&!o?"":(s==='"'&&(o=!o),s)).join("");return this.url.searchParams.set("select",a),r&&(this.headers.Prefer=`count=${r}`),new jr.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:n,defaultToNull:r=!0}={}){const i="POST",o=[];if(this.headers.Prefer&&o.push(this.headers.Prefer),n&&o.push(`count=${n}`),r||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(t)){const a=t.reduce((s,l)=>s.concat(Object.keys(l)),[]);if(a.length>0){const s=[...new Set(a)].map(l=>`"${l}"`);this.url.searchParams.set("columns",s.join(","))}}return new jr.default({method:i,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:i,defaultToNull:o=!0}={}){const a="POST",s=[`resolution=${r?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&s.push(this.headers.Prefer),i&&s.push(`count=${i}`),o||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(t)){const l=t.reduce((u,c)=>u.concat(Object.keys(c)),[]);if(l.length>0){const u=[...new Set(l)].map(c=>`"${c}"`);this.url.searchParams.set("columns",u.join(","))}}return new jr.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:n}={}){const r="PATCH",i=[];return this.headers.Prefer&&i.push(this.headers.Prefer),n&&i.push(`count=${n}`),this.headers.Prefer=i.join(","),new jr.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const n="DELETE",r=[];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new jr.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};oa.default=o0;var ua={},ca={};Object.defineProperty(ca,"__esModule",{value:!0});ca.version=void 0;ca.version="0.0.0-automated";Object.defineProperty(ua,"__esModule",{value:!0});ua.DEFAULT_HEADERS=void 0;const a0=ca;ua.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${a0.version}`};var Gf=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(du,"__esModule",{value:!0});const s0=Gf(oa),l0=Gf(yi),u0=ua;let c0=class Xf{constructor(t,{headers:n={},schema:r,fetch:i}={}){this.url=t,this.headers=Object.assign(Object.assign({},u0.DEFAULT_HEADERS),n),this.schemaName=r,this.fetch=i}from(t){const n=new URL(`${this.url}/${t}`);return new s0.default(n,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new Xf(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:i=!1,count:o}={}){let a;const s=new URL(`${this.url}/rpc/${t}`);let l;r||i?(a=r?"HEAD":"GET",Object.entries(n).filter(([c,d])=>d!==void 0).map(([c,d])=>[c,Array.isArray(d)?`{${d.join(",")}}`:`${d}`]).forEach(([c,d])=>{s.searchParams.append(c,d)})):(a="POST",l=n);const u=Object.assign({},this.headers);return o&&(u.Prefer=`count=${o}`),new l0.default({method:a,url:s,headers:u,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}};du.default=c0;var mr=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Te,"__esModule",{value:!0});Te.PostgrestError=Te.PostgrestBuilder=Te.PostgrestTransformBuilder=Te.PostgrestFilterBuilder=Te.PostgrestQueryBuilder=Te.PostgrestClient=void 0;const Yf=mr(du);Te.PostgrestClient=Yf.default;const Zf=mr(oa);Te.PostgrestQueryBuilder=Zf.default;const eh=mr(yi);Te.PostgrestFilterBuilder=eh.default;const th=mr(aa);Te.PostgrestTransformBuilder=th.default;const nh=mr(sa);Te.PostgrestBuilder=nh.default;const rh=mr(la);Te.PostgrestError=rh.default;var d0=Te.default={PostgrestClient:Yf.default,PostgrestQueryBuilder:Zf.default,PostgrestFilterBuilder:eh.default,PostgrestTransformBuilder:th.default,PostgrestBuilder:nh.default,PostgrestError:rh.default};const{PostgrestClient:p0,PostgrestQueryBuilder:d_,PostgrestFilterBuilder:p_,PostgrestTransformBuilder:f_,PostgrestBuilder:h_,PostgrestError:m_}=d0;function f0(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const h0=f0(),m0="2.11.15",v0=`realtime-js/${m0}`,g0="1.0.0",ih=1e4,y0=1e3;var Mr;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Mr||(Mr={}));var ve;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(ve||(ve={}));var Ye;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(Ye||(Ye={}));var Zs;(function(e){e.websocket="websocket"})(Zs||(Zs={}));var on;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(on||(on={}));class w0{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const i=n.getUint8(1),o=n.getUint8(2);let a=this.HEADER_LENGTH+2;const s=r.decode(t.slice(a,a+i));a=a+i;const l=r.decode(t.slice(a,a+o));a=a+o;const u=JSON.parse(r.decode(t.slice(a,t.byteLength)));return{ref:null,topic:s,event:l,payload:u}}}class oh{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var B;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(B||(B={}));const Vc=(e,t,n={})=>{var r;const i=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((o,a)=>(o[a]=x0(a,e,t,i),o),{})},x0=(e,t,n,r)=>{const i=t.find(s=>s.name===e),o=i==null?void 0:i.type,a=n[e];return o&&!r.includes(o)?ah(o,a):el(a)},ah=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return E0(t,n)}switch(e){case B.bool:return _0(t);case B.float4:case B.float8:case B.int2:case B.int4:case B.int8:case B.numeric:case B.oid:return k0(t);case B.json:case B.jsonb:return S0(t);case B.timestamp:return b0(t);case B.abstime:case B.date:case B.daterange:case B.int4range:case B.int8range:case B.money:case B.reltime:case B.text:case B.time:case B.timestamptz:case B.timetz:case B.tsrange:case B.tstzrange:return el(t);default:return el(t)}},el=e=>e,_0=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},k0=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},S0=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},E0=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let o;const a=e.slice(1,n);try{o=JSON.parse("["+a+"]")}catch{o=a?a.split(","):[]}return o.map(s=>ah(t,s))}return e},b0=e=>typeof e=="string"?e.replace(" ","T"):e,sh=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Ja{constructor(t,n,r={},i=ih){this.channel=t,this.event=n,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var Hc;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(Hc||(Hc={}));class Br{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},i=>{const{onJoin:o,onLeave:a,onSync:s}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Br.syncState(this.state,i,o,a),this.pendingDiffs.forEach(l=>{this.state=Br.syncDiff(this.state,l,o,a)}),this.pendingDiffs=[],s()}),this.channel._on(r.diff,{},i=>{const{onJoin:o,onLeave:a,onSync:s}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(i):(this.state=Br.syncDiff(this.state,i,o,a),s())}),this.onJoin((i,o,a)=>{this.channel._trigger("presence",{event:"join",key:i,currentPresences:o,newPresences:a})}),this.onLeave((i,o,a)=>{this.channel._trigger("presence",{event:"leave",key:i,currentPresences:o,leftPresences:a})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,i){const o=this.cloneDeep(t),a=this.transformState(n),s={},l={};return this.map(o,(u,c)=>{a[u]||(l[u]=c)}),this.map(a,(u,c)=>{const d=o[u];if(d){const p=c.map(_=>_.presence_ref),v=d.map(_=>_.presence_ref),g=c.filter(_=>v.indexOf(_.presence_ref)<0),w=d.filter(_=>p.indexOf(_.presence_ref)<0);g.length>0&&(s[u]=g),w.length>0&&(l[u]=w)}else s[u]=c}),this.syncDiff(o,{joins:s,leaves:l},r,i)}static syncDiff(t,n,r,i){const{joins:o,leaves:a}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(o,(s,l)=>{var u;const c=(u=t[s])!==null&&u!==void 0?u:[];if(t[s]=this.cloneDeep(l),c.length>0){const d=t[s].map(v=>v.presence_ref),p=c.filter(v=>d.indexOf(v.presence_ref)<0);t[s].unshift(...p)}r(s,c,l)}),this.map(a,(s,l)=>{let u=t[s];if(!u)return;const c=l.map(d=>d.presence_ref);u=u.filter(d=>c.indexOf(d.presence_ref)<0),t[s]=u,i(s,u,l),u.length===0&&delete t[s]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const i=t[r];return"metas"in i?n[r]=i.metas.map(o=>(o.presence_ref=o.phx_ref,delete o.phx_ref,delete o.phx_ref_prev,o)):n[r]=i,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Kc;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(Kc||(Kc={}));var Jc;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(Jc||(Jc={}));var ft;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(ft||(ft={}));class pu{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=ve.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new Ja(this,Ye.join,this.params,this.timeout),this.rejoinTimer=new oh(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=ve.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(i=>i.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=ve.closed,this.socket._remove(this)}),this._onError(i=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,i),this.state=ve.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=ve.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Ye.reply,{},(i,o)=>{this._trigger(this._replyEventName(o),i)}),this.presence=new Br(this),this.broadcastEndpointURL=sh(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.state==ve.closed){const{config:{broadcast:o,presence:a,private:s}}=this.params;this._onError(c=>t==null?void 0:t(ft.CHANNEL_ERROR,c)),this._onClose(()=>t==null?void 0:t(ft.CLOSED));const l={},u={broadcast:o,presence:a,postgres_changes:(i=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(c=>c.filter))!==null&&i!==void 0?i:[],private:s};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},l)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:c})=>{var d;if(this.socket.setAuth(),c===void 0){t==null||t(ft.SUBSCRIBED);return}else{const p=this.bindings.postgres_changes,v=(d=p==null?void 0:p.length)!==null&&d!==void 0?d:0,g=[];for(let w=0;w<v;w++){const _=p[w],{filter:{event:m,schema:h,table:f,filter:y}}=_,S=c&&c[w];if(S&&S.event===m&&S.schema===h&&S.table===f&&S.filter===y)g.push(Object.assign(Object.assign({},_),{id:S.id}));else{this.unsubscribe(),this.state=ve.errored,t==null||t(ft.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=g,t&&t(ft.SUBSCRIBED);return}}).receive("error",c=>{this.state=ve.errored,t==null||t(ft.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(ft.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this._on(t,n,r)}async send(t,n={}){var r,i;if(!this._canPush()&&t.type==="broadcast"){const{event:o,payload:a}=t,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:a,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((i=u.body)===null||i===void 0?void 0:i.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(o=>{var a,s,l;const u=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((l=(s=(a=this.params)===null||a===void 0?void 0:a.config)===null||s===void 0?void 0:s.broadcast)===null||l===void 0)&&l.ack)&&o("ok"),u.receive("ok",()=>o("ok")),u.receive("error",()=>o("error")),u.receive("timeout",()=>o("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=ve.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Ye.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(i=>{r=new Ja(this,Ye.leave,{},t),r.receive("ok",()=>{n(),i("ok")}).receive("timeout",()=>{n(),i("timed out")}).receive("error",()=>{i("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{r==null||r.destroy()})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(t,n,r){const i=new AbortController,o=setTimeout(()=>i.abort(),r),a=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:i.signal}));return clearTimeout(o),a}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new Ja(this,t,n,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var i,o;const a=t.toLocaleLowerCase(),{close:s,error:l,leave:u,join:c}=Ye;if(r&&[s,l,u,c].indexOf(a)>=0&&r!==this._joinRef())return;let p=this._onMessage(a,n,r);if(n&&!p)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(a)?(i=this.bindings.postgres_changes)===null||i===void 0||i.filter(v=>{var g,w,_;return((g=v.filter)===null||g===void 0?void 0:g.event)==="*"||((_=(w=v.filter)===null||w===void 0?void 0:w.event)===null||_===void 0?void 0:_.toLocaleLowerCase())===a}).map(v=>v.callback(p,r)):(o=this.bindings[a])===null||o===void 0||o.filter(v=>{var g,w,_,m,h,f;if(["broadcast","presence","postgres_changes"].includes(a))if("id"in v){const y=v.id,S=(g=v.filter)===null||g===void 0?void 0:g.event;return y&&((w=n.ids)===null||w===void 0?void 0:w.includes(y))&&(S==="*"||(S==null?void 0:S.toLocaleLowerCase())===((_=n.data)===null||_===void 0?void 0:_.type.toLocaleLowerCase()))}else{const y=(h=(m=v==null?void 0:v.filter)===null||m===void 0?void 0:m.event)===null||h===void 0?void 0:h.toLocaleLowerCase();return y==="*"||y===((f=n==null?void 0:n.event)===null||f===void 0?void 0:f.toLocaleLowerCase())}else return v.type.toLocaleLowerCase()===a}).map(v=>{if(typeof p=="object"&&"ids"in p){const g=p.data,{schema:w,table:_,commit_timestamp:m,type:h,errors:f}=g;p=Object.assign(Object.assign({},{schema:w,table:_,commit_timestamp:m,eventType:h,new:{},old:{},errors:f}),this._getPayloadRecords(g))}v.callback(p,r)})}_isClosed(){return this.state===ve.closed}_isJoined(){return this.state===ve.joined}_isJoining(){return this.state===ve.joining}_isLeaving(){return this.state===ve.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const i=t.toLocaleLowerCase(),o={type:i,filter:n,callback:r};return this.bindings[i]?this.bindings[i].push(o):this.bindings[i]=[o],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(i=>{var o;return!(((o=i.type)===null||o===void 0?void 0:o.toLocaleLowerCase())===r&&pu.isEqual(i.filter,n))}),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(Ye.close,{},t)}_onError(t){this._on(Ye.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=ve.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=Vc(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=Vc(t.columns,t.old_record)),n}}const Qc=()=>{},j0=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class C0{constructor(t,n){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=ih,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Qc,this.ref=0,this.logger=Qc,this.conn=null,this.sendBuffer=[],this.serializer=new w0,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=o=>{let a;return o?a=o:typeof fetch>"u"?a=(...s)=>gi(()=>Promise.resolve().then(()=>hr),void 0).then(({default:l})=>l(...s)):a=fetch,(...s)=>a(...s)},this.endPoint=`${t}/${Zs.websocket}`,this.httpEndpoint=sh(t),n!=null&&n.transport?this.transport=n.transport:this.transport=null,n!=null&&n.params&&(this.params=n.params),n!=null&&n.timeout&&(this.timeout=n.timeout),n!=null&&n.logger&&(this.logger=n.logger),(n!=null&&n.logLevel||n!=null&&n.log_level)&&(this.logLevel=n.logLevel||n.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),n!=null&&n.heartbeatIntervalMs&&(this.heartbeatIntervalMs=n.heartbeatIntervalMs);const i=(r=n==null?void 0:n.params)===null||r===void 0?void 0:r.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=n!=null&&n.reconnectAfterMs?n.reconnectAfterMs:o=>[1e3,2e3,5e3,1e4][o-1]||1e4,this.encode=n!=null&&n.encode?n.encode:(o,a)=>a(JSON.stringify(o)),this.decode=n!=null&&n.decode?n.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new oh(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(n==null?void 0:n.fetch),n!=null&&n.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(n==null?void 0:n.worker)||!1,this.workerUrl=n==null?void 0:n.workerUrl}this.accessToken=(n==null?void 0:n.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=h0),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:g0}))}disconnect(t,n){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,n??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Mr.connecting:return on.Connecting;case Mr.open:return on.Open;case Mr.closing:return on.Closing;default:return on.Closed}}isConnected(){return this.connectionState()===on.Open}channel(t,n={config:{}}){const r=`realtime:${t}`,i=this.getChannels().find(o=>o.topic===r);if(i)return i;{const o=new pu(`realtime:${t}`,n,this);return this.channels.push(o),o}}push(t){const{topic:n,event:r,payload:i,ref:o}=t,a=()=>{this.encode(t,s=>{var l;(l=this.conn)===null||l===void 0||l.send(s)})};this.log("push",`${n} ${r} (${o})`,i),this.isConnected()?a():this.sendBuffer.push(a)}async setAuth(t=null){let n=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=n&&(this.accessTokenValue=n,this.channels.forEach(r=>{const i={access_token:n,version:v0};n&&r.updateJoinPayload(i),r.joinedOnce&&r._isJoined()&&r._push(Ye.access_token,{access_token:n})}))}async sendHeartbeat(){var t;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(t=this.conn)===null||t===void 0||t.close(y0,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n.topic!==t.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,n=>{let{topic:r,event:i,payload:o,ref:a}=n;r==="phoenix"&&i==="phx_reply"&&this.heartbeatCallback(n.payload.status=="ok"?"ok":"error"),a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${o.status||""} ${r} ${i} ${a&&"("+a+")"||""}`,o),Array.from(this.channels).filter(s=>s._isMember(r)).forEach(s=>s._trigger(i,o,a)),this.stateChangeCallbacks.message.forEach(s=>s(n))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(t=>t())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(n=>n(t))}_onConnError(t){this.log("transport",`${t}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(n=>n(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(Ye.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",i=new URLSearchParams(n);return`${t}${r}${i}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([j0],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}}class fu extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function le(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class P0 extends fu{constructor(t,n){super(t),this.name="StorageApiError",this.status=n}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class tl extends fu{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var T0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};const lh=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>gi(()=>Promise.resolve().then(()=>hr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},O0=()=>T0(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield gi(()=>Promise.resolve().then(()=>hr),void 0)).Response:Response}),nl=e=>{if(Array.isArray(e))return e.map(n=>nl(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const i=n.replace(/([-_][a-z])/gi,o=>o.toUpperCase().replace(/[-_]/g,""));t[i]=nl(r)}),t};var xn=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};const Qa=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),R0=(e,t,n)=>xn(void 0,void 0,void 0,function*(){const r=yield O0();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(i=>{t(new P0(Qa(i),e.status||500))}).catch(i=>{t(new tl(Qa(i),i))}):t(new tl(Qa(e),e))}),I0=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),n))};function wi(e,t,n,r,i,o){return xn(this,void 0,void 0,function*(){return new Promise((a,s)=>{e(n,I0(t,r,i,o)).then(l=>{if(!l.ok)throw l;return r!=null&&r.noResolveJson?l:l.json()}).then(l=>a(l)).catch(l=>R0(l,s,r))})})}function Do(e,t,n,r){return xn(this,void 0,void 0,function*(){return wi(e,"GET",t,n,r)})}function Ct(e,t,n,r,i){return xn(this,void 0,void 0,function*(){return wi(e,"POST",t,r,i,n)})}function A0(e,t,n,r,i){return xn(this,void 0,void 0,function*(){return wi(e,"PUT",t,r,i,n)})}function D0(e,t,n,r){return xn(this,void 0,void 0,function*(){return wi(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function uh(e,t,n,r,i){return xn(this,void 0,void 0,function*(){return wi(e,"DELETE",t,r,i,n)})}var Ce=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};const L0={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Gc={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class $0{constructor(t,n={},r,i){this.url=t,this.headers=n,this.bucketId=r,this.fetch=lh(i)}uploadOrUpdate(t,n,r,i){return Ce(this,void 0,void 0,function*(){try{let o;const a=Object.assign(Object.assign({},Gc),i);let s=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(a.upsert)});const l=a.metadata;typeof Blob<"u"&&r instanceof Blob?(o=new FormData,o.append("cacheControl",a.cacheControl),l&&o.append("metadata",this.encodeMetadata(l)),o.append("",r)):typeof FormData<"u"&&r instanceof FormData?(o=r,o.append("cacheControl",a.cacheControl),l&&o.append("metadata",this.encodeMetadata(l))):(o=r,s["cache-control"]=`max-age=${a.cacheControl}`,s["content-type"]=a.contentType,l&&(s["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),i!=null&&i.headers&&(s=Object.assign(Object.assign({},s),i.headers));const u=this._removeEmptyFolders(n),c=this._getFinalPath(u),d=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:o,headers:s},a!=null&&a.duplex?{duplex:a.duplex}:{})),p=yield d.json();return d.ok?{data:{path:u,id:p.Id,fullPath:p.Key},error:null}:{data:null,error:p}}catch(o){if(le(o))return{data:null,error:o};throw o}})}upload(t,n,r){return Ce(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,i){return Ce(this,void 0,void 0,function*(){const o=this._removeEmptyFolders(t),a=this._getFinalPath(o),s=new URL(this.url+`/object/upload/sign/${a}`);s.searchParams.set("token",n);try{let l;const u=Object.assign({upsert:Gc.upsert},i),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&r instanceof Blob?(l=new FormData,l.append("cacheControl",u.cacheControl),l.append("",r)):typeof FormData<"u"&&r instanceof FormData?(l=r,l.append("cacheControl",u.cacheControl)):(l=r,c["cache-control"]=`max-age=${u.cacheControl}`,c["content-type"]=u.contentType);const d=yield this.fetch(s.toString(),{method:"PUT",body:l,headers:c}),p=yield d.json();return d.ok?{data:{path:o,fullPath:p.Key},error:null}:{data:null,error:p}}catch(l){if(le(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,n){return Ce(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const i=Object.assign({},this.headers);n!=null&&n.upsert&&(i["x-upsert"]="true");const o=yield Ct(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),a=new URL(this.url+o.url),s=a.searchParams.get("token");if(!s)throw new fu("No token returned by API");return{data:{signedUrl:a.toString(),path:t,token:s},error:null}}catch(r){if(le(r))return{data:null,error:r};throw r}})}update(t,n,r){return Ce(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return Ce(this,void 0,void 0,function*(){try{return{data:yield Ct(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}copy(t,n,r){return Ce(this,void 0,void 0,function*(){try{return{data:{path:(yield Ct(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}createSignedUrl(t,n,r){return Ce(this,void 0,void 0,function*(){try{let i=this._getFinalPath(t),o=yield Ct(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const a=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return o={signedUrl:encodeURI(`${this.url}${o.signedURL}${a}`)},{data:o,error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}createSignedUrls(t,n,r){return Ce(this,void 0,void 0,function*(){try{const i=yield Ct(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:i.map(a=>Object.assign(Object.assign({},a),{signedUrl:a.signedURL?encodeURI(`${this.url}${a.signedURL}${o}`):null})),error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}download(t,n){return Ce(this,void 0,void 0,function*(){const i=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",o=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),a=o?`?${o}`:"";try{const s=this._getFinalPath(t);return{data:yield(yield Do(this.fetch,`${this.url}/${i}/${s}${a}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(s){if(le(s))return{data:null,error:s};throw s}})}info(t){return Ce(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield Do(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:nl(r),error:null}}catch(r){if(le(r))return{data:null,error:r};throw r}})}exists(t){return Ce(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield D0(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(le(r)&&r instanceof tl){const i=r.originalError;if([400,404].includes(i==null?void 0:i.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),i=[],o=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";o!==""&&i.push(o);const s=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});l!==""&&i.push(l);let u=i.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${s}/public/${r}${u}`)}}}remove(t){return Ce(this,void 0,void 0,function*(){try{return{data:yield uh(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(le(n))return{data:null,error:n};throw n}})}list(t,n,r){return Ce(this,void 0,void 0,function*(){try{const i=Object.assign(Object.assign(Object.assign({},L0),n),{prefix:t||""});return{data:yield Ct(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const N0="2.7.1",z0={"X-Client-Info":`storage-js/${N0}`};var Pn=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};class U0{constructor(t,n={},r){this.url=t,this.headers=Object.assign(Object.assign({},z0),n),this.fetch=lh(r)}listBuckets(){return Pn(this,void 0,void 0,function*(){try{return{data:yield Do(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(le(t))return{data:null,error:t};throw t}})}getBucket(t){return Pn(this,void 0,void 0,function*(){try{return{data:yield Do(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(le(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return Pn(this,void 0,void 0,function*(){try{return{data:yield Ct(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(le(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return Pn(this,void 0,void 0,function*(){try{return{data:yield A0(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(le(r))return{data:null,error:r};throw r}})}emptyBucket(t){return Pn(this,void 0,void 0,function*(){try{return{data:yield Ct(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(le(n))return{data:null,error:n};throw n}})}deleteBucket(t){return Pn(this,void 0,void 0,function*(){try{return{data:yield uh(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(le(n))return{data:null,error:n};throw n}})}}class F0 extends U0{constructor(t,n={},r){super(t,n,r)}from(t){return new $0(this.url,this.headers,t,this.fetch)}}const M0="2.50.2";let Rr="";typeof Deno<"u"?Rr="deno":typeof document<"u"?Rr="web":typeof navigator<"u"&&navigator.product==="ReactNative"?Rr="react-native":Rr="node";const B0={"X-Client-Info":`supabase-js-${Rr}/${M0}`},W0={headers:B0},q0={schema:"public"},V0={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},H0={};var K0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};const J0=e=>{let t;return e?t=e:typeof fetch>"u"?t=Kf:t=fetch,(...n)=>t(...n)},Q0=()=>typeof Headers>"u"?Jf:Headers,G0=(e,t,n)=>{const r=J0(n),i=Q0();return(o,a)=>K0(void 0,void 0,void 0,function*(){var s;const l=(s=yield t())!==null&&s!==void 0?s:e;let u=new i(a==null?void 0:a.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${l}`),r(o,Object.assign(Object.assign({},a),{headers:u}))})};var X0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};function Y0(e){return e.endsWith("/")?e:e+"/"}function Z0(e,t){var n,r;const{db:i,auth:o,realtime:a,global:s}=e,{db:l,auth:u,realtime:c,global:d}=t,p={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},u),o),realtime:Object.assign(Object.assign({},c),a),global:Object.assign(Object.assign(Object.assign({},d),s),{headers:Object.assign(Object.assign({},(n=d==null?void 0:d.headers)!==null&&n!==void 0?n:{}),(r=s==null?void 0:s.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>X0(this,void 0,void 0,function*(){return""})};return e.accessToken?p.accessToken=e.accessToken:delete p.accessToken,p}const ch="2.70.0",An=30*1e3,rl=3,Ga=rl*An,ew="http://localhost:9999",tw="supabase.auth.token",nw={"X-Client-Info":`gotrue-js/${ch}`},il="X-Supabase-Api-Version",dh={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},rw=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,iw=6e5;class hu extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function A(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class ow extends hu{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function aw(e){return A(e)&&e.name==="AuthApiError"}class ph extends hu{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class Xt extends hu{constructor(t,n,r,i){super(t,r,i),this.name=n,this.status=r}}class Et extends Xt{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function sw(e){return A(e)&&e.name==="AuthSessionMissingError"}class Bi extends Xt{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Wi extends Xt{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class qi extends Xt{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function lw(e){return A(e)&&e.name==="AuthImplicitGrantRedirectError"}class Xc extends Xt{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ol extends Xt{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function Xa(e){return A(e)&&e.name==="AuthRetryableFetchError"}class Yc extends Xt{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}class Wr extends Xt{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const Lo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Zc=` 	
\r=`.split(""),uw=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Zc.length;t+=1)e[Zc[t].charCodeAt(0)]=-2;for(let t=0;t<Lo.length;t+=1)e[Lo[t].charCodeAt(0)]=t;return e})();function ed(e,t,n){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(Lo[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(Lo[r]),t.queuedBits-=6}}function fh(e,t,n){const r=uw[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function td(e){const t=[],n=a=>{t.push(String.fromCodePoint(a))},r={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},o=a=>{pw(a,r,n)};for(let a=0;a<e.length;a+=1)fh(e.charCodeAt(a),i,o);return t.join("")}function cw(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function dw(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const i=(r-55296)*1024&65535;r=(e.charCodeAt(n+1)-56320&65535|i)+65536,n+=1}cw(r,t)}}function pw(e,t,n){if(t.utf8seq===0){if(e<=127){n(e);return}for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&n(t.codepoint)}}function fw(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};for(let i=0;i<e.length;i+=1)fh(e.charCodeAt(i),n,r);return new Uint8Array(t)}function hw(e){const t=[];return dw(e,n=>t.push(n)),new Uint8Array(t)}function mw(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};return e.forEach(i=>ed(i,n,r)),ed(null,n,r),t.join("")}function vw(e){return Math.round(Date.now()/1e3)+e}function gw(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const Qe=()=>typeof window<"u"&&typeof document<"u",en={tested:!1,writable:!1},qr=()=>{if(!Qe())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(en.tested)return en.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),en.tested=!0,en.writable=!0}catch{en.tested=!0,en.writable=!1}return en.writable};function yw(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((i,o)=>{t[o]=i})}catch{}return n.searchParams.forEach((r,i)=>{t[i]=r}),t}const hh=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>gi(()=>Promise.resolve().then(()=>hr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},ww=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",mh=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},Vi=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},Hi=async(e,t)=>{await e.removeItem(t)};class da{constructor(){this.promise=new da.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}da.promiseConstructor=Promise;function Ya(e){const t=e.split(".");if(t.length!==3)throw new Wr("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!rw.test(t[r]))throw new Wr("JWT not in base64url format");return{header:JSON.parse(td(t[0])),payload:JSON.parse(td(t[1])),signature:fw(t[2]),raw:{header:t[0],payload:t[1]}}}async function xw(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function _w(e,t){return new Promise((r,i)=>{(async()=>{for(let o=0;o<1/0;o++)try{const a=await e(o);if(!t(o,null,a)){r(a);return}}catch(a){if(!t(o,a)){i(a);return}}})()})}function kw(e){return("0"+e.toString(16)).substr(-2)}function Sw(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let i="";for(let o=0;o<56;o++)i+=n.charAt(Math.floor(Math.random()*r));return i}return crypto.getRandomValues(t),Array.from(t,kw).join("")}async function Ew(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),i=new Uint8Array(r);return Array.from(i).map(o=>String.fromCharCode(o)).join("")}async function bw(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await Ew(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Tn(e,t,n=!1){const r=Sw();let i=r;n&&(i+="/PASSWORD_RECOVERY"),await mh(e,`${t}-code-verifier`,i);const o=await bw(r);return[o,r===o?"plain":"s256"]}const jw=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Cw(e){const t=e.headers.get(il);if(!t||!t.match(jw))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function Pw(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function Tw(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const Ow=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function On(e){if(!Ow.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var Rw=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};const rn=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Iw=[502,503,504];async function nd(e){var t;if(!ww(e))throw new ol(rn(e),0);if(Iw.includes(e.status))throw new ol(rn(e),e.status);let n;try{n=await e.json()}catch(o){throw new ph(rn(o),o)}let r;const i=Cw(e);if(i&&i.getTime()>=dh["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new Yc(rn(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new Et}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((o,a)=>o&&typeof a=="string",!0))throw new Yc(rn(n),e.status,n.weak_password.reasons);throw new ow(rn(n),e.status||500,r)}const Aw=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),n))};async function $(e,t,n,r){var i;const o=Object.assign({},r==null?void 0:r.headers);o[il]||(o[il]=dh["2024-01-01"].name),r!=null&&r.jwt&&(o.Authorization=`Bearer ${r.jwt}`);const a=(i=r==null?void 0:r.query)!==null&&i!==void 0?i:{};r!=null&&r.redirectTo&&(a.redirect_to=r.redirectTo);const s=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await Dw(e,t,n+s,{headers:o,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(l):{data:Object.assign({},l),error:null}}async function Dw(e,t,n,r,i,o){const a=Aw(t,r,i,o);let s;try{s=await e(n,Object.assign({},a))}catch(l){throw console.error(l),new ol(rn(l),0)}if(s.ok||await nd(s),r!=null&&r.noResolveJson)return s;try{return await s.json()}catch(l){await nd(l)}}function dt(e){var t;let n=null;zw(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=vw(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function rd(e){const t=dt(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function Ot(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function Lw(e){return{data:e,error:null}}function $w(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:o}=e,a=Rw(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),s={action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:o},l=Object.assign({},a);return{data:{properties:s,user:l},error:null}}function Nw(e){return e}function zw(e){return e.access_token&&e.refresh_token&&e.expires_in}const Za=["global","local","others"];var Uw=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};class Fw{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=hh(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n=Za[0]){if(Za.indexOf(n)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Za.join(", ")}`);try{return await $(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(A(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await $(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:Ot})}catch(r){if(A(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=Uw(t,["options"]),i=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(i.new_email=r==null?void 0:r.newEmail,delete i.newEmail),await $(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:$w,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(A(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await $(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:Ot})}catch(n){if(A(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,i,o,a,s,l;try{const u={nextPage:null,lastPage:0,total:0},c=await $(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(o=(i=t==null?void 0:t.perPage)===null||i===void 0?void 0:i.toString())!==null&&o!==void 0?o:""},xform:Nw});if(c.error)throw c.error;const d=await c.json(),p=(a=c.headers.get("x-total-count"))!==null&&a!==void 0?a:0,v=(l=(s=c.headers.get("link"))===null||s===void 0?void 0:s.split(","))!==null&&l!==void 0?l:[];return v.length>0&&(v.forEach(g=>{const w=parseInt(g.split(";")[0].split("=")[1].substring(0,1)),_=JSON.parse(g.split(";")[1].split("=")[1]);u[`${_}Page`]=w}),u.total=parseInt(p)),{data:Object.assign(Object.assign({},d),u),error:null}}catch(u){if(A(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){On(t);try{return await $(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:Ot})}catch(n){if(A(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){On(t);try{return await $(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:Ot})}catch(r){if(A(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){On(t);try{return await $(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:Ot})}catch(r){if(A(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){On(t.userId);try{const{data:n,error:r}=await $(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:i=>({data:{factors:i},error:null})});return{data:n,error:r}}catch(n){if(A(n))return{data:null,error:n};throw n}}async _deleteFactor(t){On(t.userId),On(t.id);try{return{data:await $(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(A(n))return{data:null,error:n};throw n}}}const Mw={getItem:e=>qr()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{qr()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{qr()&&globalThis.localStorage.removeItem(e)}};function id(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function Bw(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const Rn={debug:!!(globalThis&&qr()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class vh extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class Ww extends vh{}async function qw(e,t,n){Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async i=>{if(i){Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await n()}finally{Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(t===0)throw Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Ww(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(Rn.debug)try{const o=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(o,null,"  "))}catch(o){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",o)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}Bw();const Vw={url:ew,storageKey:tw,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:nw,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function od(e,t,n){return await n()}class ci{constructor(t){var n,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ci.nextInstanceID,ci.nextInstanceID+=1,this.instanceID>0&&Qe()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const i=Object.assign(Object.assign({},Vw),t);if(this.logDebugMessages=!!i.debug,typeof i.debug=="function"&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new Fw({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=hh(i.fetch),this.lock=i.lock||od,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:Qe()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=qw:this.lock=od,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:qr()?this.storage=Mw:(this.memoryStorage={},this.storage=id(this.memoryStorage)):(this.memoryStorage={},this.storage=id(this.memoryStorage)),Qe()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(o){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",o)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async o=>{this._debug("received broadcast notification from other tab or client",o),await this._notifyAllSubscribers(o.data.event,o.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${ch}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=yw(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),Qe()&&this.detectSessionInUrl&&r!=="none"){const{data:i,error:o}=await this._getSessionFromURL(n,r);if(o){if(this._debug("#_initialize()","error detecting session from URL",o),lw(o)){const l=(t=o.details)===null||t===void 0?void 0:t.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:o}}return await this._removeSession(),{error:o}}const{session:a,redirectType:s}=i;return this._debug("#_initialize()","detected session in URL",a,"redirect type",s),await this._saveSession(a),setTimeout(async()=>{s==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",a):await this._notifyAllSubscribers("SIGNED_IN",a)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return A(n)?{error:n}:{error:new ph("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,i;try{const o=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(i=t==null?void 0:t.options)===null||i===void 0?void 0:i.captchaToken}},xform:dt}),{data:a,error:s}=o;if(s||!a)return{data:{user:null,session:null},error:s};const l=a.session,u=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(o){if(A(o))return{data:{user:null,session:null},error:o};throw o}}async signUp(t){var n,r,i;try{let o;if("email"in t){const{email:c,password:d,options:p}=t;let v=null,g=null;this.flowType==="pkce"&&([v,g]=await Tn(this.storage,this.storageKey)),o=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:p==null?void 0:p.emailRedirectTo,body:{email:c,password:d,data:(n=p==null?void 0:p.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken},code_challenge:v,code_challenge_method:g},xform:dt})}else if("phone"in t){const{phone:c,password:d,options:p}=t;o=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:d,data:(r=p==null?void 0:p.data)!==null&&r!==void 0?r:{},channel:(i=p==null?void 0:p.channel)!==null&&i!==void 0?i:"sms",gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken}},xform:dt})}else throw new Wi("You must provide either an email or phone number and a password");const{data:a,error:s}=o;if(s||!a)return{data:{user:null,session:null},error:s};const l=a.session,u=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(o){if(A(o))return{data:{user:null,session:null},error:o};throw o}}async signInWithPassword(t){try{let n;if("email"in t){const{email:o,password:a,options:s}=t;n=await $(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:o,password:a,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}},xform:rd})}else if("phone"in t){const{phone:o,password:a,options:s}=t;n=await $(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:o,password:a,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}},xform:rd})}else throw new Wi("You must provide either an email or phone number and a password");const{data:r,error:i}=n;return i?{data:{user:null,session:null},error:i}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new Bi}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i})}catch(n){if(A(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,i,o;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(i=t.options)===null||i===void 0?void 0:i.queryParams,skipBrowserRedirect:(o=t.options)===null||o===void 0?void 0:o.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:n}=t;if(n==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${n}"`)}async signInWithSolana(t){var n,r,i,o,a,s,l,u,c,d,p,v;let g,w;if("message"in t)g=t.message,w=t.signature;else{const{chain:_,wallet:m,statement:h,options:f}=t;let y;if(Qe())if(typeof m=="object")y=m;else{const b=window;if("solana"in b&&typeof b.solana=="object"&&("signIn"in b.solana&&typeof b.solana.signIn=="function"||"signMessage"in b.solana&&typeof b.solana.signMessage=="function"))y=b.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof m!="object"||!(f!=null&&f.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");y=m}const S=new URL((n=f==null?void 0:f.url)!==null&&n!==void 0?n:window.location.href);if("signIn"in y&&y.signIn){const b=await y.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},f==null?void 0:f.signInWithSolana),{version:"1",domain:S.host,uri:S.href}),h?{statement:h}:null));let j;if(Array.isArray(b)&&b[0]&&typeof b[0]=="object")j=b[0];else if(b&&typeof b=="object"&&"signedMessage"in b&&"signature"in b)j=b;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in j&&"signature"in j&&(typeof j.signedMessage=="string"||j.signedMessage instanceof Uint8Array)&&j.signature instanceof Uint8Array)g=typeof j.signedMessage=="string"?j.signedMessage:new TextDecoder().decode(j.signedMessage),w=j.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in y)||typeof y.signMessage!="function"||!("publicKey"in y)||typeof y!="object"||!y.publicKey||!("toBase58"in y.publicKey)||typeof y.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");g=[`${S.host} wants you to sign in with your Solana account:`,y.publicKey.toBase58(),...h?["",h,""]:[""],"Version: 1",`URI: ${S.href}`,`Issued At: ${(i=(r=f==null?void 0:f.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&i!==void 0?i:new Date().toISOString()}`,...!((o=f==null?void 0:f.signInWithSolana)===null||o===void 0)&&o.notBefore?[`Not Before: ${f.signInWithSolana.notBefore}`]:[],...!((a=f==null?void 0:f.signInWithSolana)===null||a===void 0)&&a.expirationTime?[`Expiration Time: ${f.signInWithSolana.expirationTime}`]:[],...!((s=f==null?void 0:f.signInWithSolana)===null||s===void 0)&&s.chainId?[`Chain ID: ${f.signInWithSolana.chainId}`]:[],...!((l=f==null?void 0:f.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${f.signInWithSolana.nonce}`]:[],...!((u=f==null?void 0:f.signInWithSolana)===null||u===void 0)&&u.requestId?[`Request ID: ${f.signInWithSolana.requestId}`]:[],...!((d=(c=f==null?void 0:f.signInWithSolana)===null||c===void 0?void 0:c.resources)===null||d===void 0)&&d.length?["Resources",...f.signInWithSolana.resources.map(j=>`- ${j}`)]:[]].join(`
`);const b=await y.signMessage(new TextEncoder().encode(g),"utf8");if(!b||!(b instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");w=b}}try{const{data:_,error:m}=await $(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:g,signature:mw(w)},!((p=t.options)===null||p===void 0)&&p.captchaToken?{gotrue_meta_security:{captcha_token:(v=t.options)===null||v===void 0?void 0:v.captchaToken}}:null),xform:dt});if(m)throw m;return!_||!_.session||!_.user?{data:{user:null,session:null},error:new Bi}:(_.session&&(await this._saveSession(_.session),await this._notifyAllSubscribers("SIGNED_IN",_.session)),{data:Object.assign({},_),error:m})}catch(_){if(A(_))return{data:{user:null,session:null},error:_};throw _}}async _exchangeCodeForSession(t){const n=await Vi(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(n??"").split("/");try{const{data:o,error:a}=await $(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:dt});if(await Hi(this.storage,`${this.storageKey}-code-verifier`),a)throw a;return!o||!o.session||!o.user?{data:{user:null,session:null,redirectType:null},error:new Bi}:(o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:Object.assign(Object.assign({},o),{redirectType:i??null}),error:a})}catch(o){if(A(o))return{data:{user:null,session:null,redirectType:null},error:o};throw o}}async signInWithIdToken(t){try{const{options:n,provider:r,token:i,access_token:o,nonce:a}=t,s=await $(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:o,nonce:a,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:dt}),{data:l,error:u}=s;return u?{data:{user:null,session:null},error:u}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new Bi}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:u})}catch(n){if(A(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,i,o,a;try{if("email"in t){const{email:s,options:l}=t;let u=null,c=null;this.flowType==="pkce"&&([u,c]=await Tn(this.storage,this.storageKey));const{error:d}=await $(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:(n=l==null?void 0:l.data)!==null&&n!==void 0?n:{},create_user:(r=l==null?void 0:l.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:u,code_challenge_method:c},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in t){const{phone:s,options:l}=t,{data:u,error:c}=await $(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:s,data:(i=l==null?void 0:l.data)!==null&&i!==void 0?i:{},create_user:(o=l==null?void 0:l.shouldCreateUser)!==null&&o!==void 0?o:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(a=l==null?void 0:l.channel)!==null&&a!==void 0?a:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:c}}throw new Wi("You must provide either an email or phone number.")}catch(s){if(A(s))return{data:{user:null,session:null},error:s};throw s}}async verifyOtp(t){var n,r;try{let i,o;"options"in t&&(i=(n=t.options)===null||n===void 0?void 0:n.redirectTo,o=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:a,error:s}=await $(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:o}}),redirectTo:i,xform:dt});if(s)throw s;if(!a)throw new Error("An error occurred on token verification.");const l=a.session,u=a.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(A(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithSSO(t){var n,r,i;try{let o=null,a=null;return this.flowType==="pkce"&&([o,a]=await Tn(this.storage,this.storageKey)),await $(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((i=t==null?void 0:t.options)===null||i===void 0)&&i.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:o,code_challenge_method:a}),headers:this.headers,xform:Lw})}catch(o){if(A(o))return{data:null,error:o};throw o}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new Et;const{error:i}=await $(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:i}})}catch(t){if(A(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:i,options:o}=t,{error:a}=await $(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},redirectTo:o==null?void 0:o.emailRedirectTo});return{data:{user:null,session:null},error:a}}else if("phone"in t){const{phone:r,type:i,options:o}=t,{data:a,error:s}=await $(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}}});return{data:{user:null,session:null,messageId:a==null?void 0:a.message_id},error:s}}throw new Wi("You must provide either an email or phone number and a type")}catch(n){if(A(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),i=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await i}catch{}})()),i}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const i=[...this.pendingInLock];await Promise.all(i),this.pendingInLock.splice(0,i.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await Vi(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<Ga:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let a=this.suppressGetSessionWarning;t=new Proxy(t,{get:(l,u,c)=>(!a&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),a=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,u,c))})}return{data:{session:t},error:null}}const{session:i,error:o}=await this._callRefreshToken(t.refresh_token);return o?{data:{session:null},error:o}:{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await $(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:Ot}):await this._useSession(async n=>{var r,i,o;const{data:a,error:s}=n;if(s)throw s;return!(!((r=a.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new Et}:await $(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(o=(i=a.session)===null||i===void 0?void 0:i.access_token)!==null&&o!==void 0?o:void 0,xform:Ot})})}catch(n){if(A(n))return sw(n)&&(await this._removeSession(),await Hi(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:i,error:o}=r;if(o)throw o;if(!i.session)throw new Et;const a=i.session;let s=null,l=null;this.flowType==="pkce"&&t.email!=null&&([s,l]=await Tn(this.storage,this.storageKey));const{data:u,error:c}=await $(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:s,code_challenge_method:l}),jwt:a.access_token,xform:Ot});if(c)throw c;return a.user=u.user,await this._saveSession(a),await this._notifyAllSubscribers("USER_UPDATED",a),{data:{user:a.user},error:null}})}catch(r){if(A(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new Et;const n=Date.now()/1e3;let r=n,i=!0,o=null;const{payload:a}=Ya(t.access_token);if(a.exp&&(r=a.exp,i=r<=n),i){const{session:s,error:l}=await this._callRefreshToken(t.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!s)return{data:{user:null,session:null},error:null};o=s}else{const{data:s,error:l}=await this._getUser(t.access_token);if(l)throw l;o={access_token:t.access_token,refresh_token:t.refresh_token,user:s.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(o),await this._notifyAllSubscribers("SIGNED_IN",o)}return{data:{user:o.user,session:o},error:null}}catch(n){if(A(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:a,error:s}=n;if(s)throw s;t=(r=a.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new Et;const{session:i,error:o}=await this._callRefreshToken(t.refresh_token);return o?{data:{user:null,session:null},error:o}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(A(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!Qe())throw new qi("No browser detected.");if(t.error||t.error_description||t.error_code)throw new qi(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new Xc("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new qi("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new Xc("No code detected.");const{data:h,error:f}=await this._exchangeCodeForSession(t.code);if(f)throw f;const y=new URL(window.location.href);return y.searchParams.delete("code"),window.history.replaceState(window.history.state,"",y.toString()),{data:{session:h.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:i,access_token:o,refresh_token:a,expires_in:s,expires_at:l,token_type:u}=t;if(!o||!s||!a||!u)throw new qi("No session defined in URL");const c=Math.round(Date.now()/1e3),d=parseInt(s);let p=c+d;l&&(p=parseInt(l));const v=p-c;v*1e3<=An&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${v}s, should have been closer to ${d}s`);const g=p-d;c-g>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",g,p,c):c-g<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",g,p,c);const{data:w,error:_}=await this._getUser(o);if(_)throw _;const m={provider_token:r,provider_refresh_token:i,access_token:o,expires_in:d,expires_at:p,refresh_token:a,token_type:u,user:w.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:t.type},error:null}}catch(r){if(A(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await Vi(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:i,error:o}=n;if(o)return{error:o};const a=(r=i.session)===null||r===void 0?void 0:r.access_token;if(a){const{error:s}=await this.admin.signOut(a,t);if(s&&!(aw(s)&&(s.status===404||s.status===401||s.status===403)))return{error:s}}return t!=="others"&&(await this._removeSession(),await Hi(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=gw(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,i;try{const{data:{session:o},error:a}=n;if(a)throw a;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",o)),this._debug("INITIAL_SESSION","callback id",t,"session",o)}catch(o){await((i=this.stateChangeEmitters.get(t))===null||i===void 0?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",o),console.error(o)}})}async resetPasswordForEmail(t,n={}){let r=null,i=null;this.flowType==="pkce"&&([r,i]=await Tn(this.storage,this.storageKey,!0));try{return await $(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(o){if(A(o))return{data:null,error:o};throw o}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(A(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:i}=await this._useSession(async o=>{var a,s,l,u,c;const{data:d,error:p}=o;if(p)throw p;const v=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(a=t.options)===null||a===void 0?void 0:a.redirectTo,scopes:(s=t.options)===null||s===void 0?void 0:s.scopes,queryParams:(l=t.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await $(this.fetch,"GET",v,{headers:this.headers,jwt:(c=(u=d.session)===null||u===void 0?void 0:u.access_token)!==null&&c!==void 0?c:void 0})});if(i)throw i;return Qe()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(A(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,i;const{data:o,error:a}=n;if(a)throw a;return await $(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(i=(r=o.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:void 0})})}catch(n){if(A(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await _w(async i=>(i>0&&await xw(200*Math.pow(2,i-1)),this._debug(n,"refreshing attempt",i),await $(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:dt})),(i,o)=>{const a=200*Math.pow(2,i);return o&&Xa(o)&&Date.now()+a-r<An})}catch(r){if(this._debug(n,"error",r),A(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),Qe()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const r=await Vi(this.storage,this.storageKey);if(this._debug(n,"session from storage",r),!this._isValidSession(r)){this._debug(n,"session is not valid"),r!==null&&await this._removeSession();return}const i=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<Ga;if(this._debug(n,`session has${i?"":" not"} expired with margin of ${Ga}s`),i){if(this.autoRefreshToken&&r.refresh_token){const{error:o}=await this._callRefreshToken(r.refresh_token);o&&(console.error(o),Xa(o)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",o),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(n,"error",r),console.error(r);return}finally{this._debug(n,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new Et;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const i=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new da;const{data:o,error:a}=await this._refreshAccessToken(t);if(a)throw a;if(!o.session)throw new Et;await this._saveSession(o.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",o.session);const s={session:o.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(o){if(this._debug(i,"error",o),A(o)){const a={session:null,error:o};return Xa(o)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(a),a}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(o),o}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(t,n,r=!0){const i=`#_notifyAllSubscribers(${t})`;this._debug(i,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const o=[],a=Array.from(this.stateChangeEmitters.values()).map(async s=>{try{await s.callback(t,n)}catch(l){o.push(l)}});if(await Promise.all(a),o.length>0){for(let s=0;s<o.length;s+=1)console.error(o[s]);throw o[0]}}finally{this._debug(i,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await mh(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Hi(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&Qe()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),An);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const i=Math.floor((r.expires_at*1e3-t)/An);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts ${An}ms, refresh threshold is ${rl} ticks`),i<=rl&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof vh)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Qe()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const i=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[o,a]=await Tn(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(o)}`,code_challenge_method:`${encodeURIComponent(a)}`});i.push(s.toString())}if(r!=null&&r.queryParams){const o=new URLSearchParams(r.queryParams);i.push(o.toString())}return r!=null&&r.skipBrowserRedirect&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${i.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:i,error:o}=n;return o?{data:null,error:o}:await $(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(A(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,i;const{data:o,error:a}=n;if(a)return{data:null,error:a};const s=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:l,error:u}=await $(this.fetch,"POST",`${this.url}/factors`,{body:s,headers:this.headers,jwt:(r=o==null?void 0:o.session)===null||r===void 0?void 0:r.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((i=l==null?void 0:l.totp)===null||i===void 0)&&i.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(n){if(A(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:o}=n;if(o)return{data:null,error:o};const{data:a,error:s}=await $(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token});return s?{data:null,error:s}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+a.expires_in},a)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",a),{data:a,error:s})})}catch(n){if(A(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:o}=n;return o?{data:null,error:o}:await $(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(A(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],i=r.filter(a=>a.factor_type==="totp"&&a.status==="verified"),o=r.filter(a=>a.factor_type==="phone"&&a.status==="verified");return{data:{all:r,totp:i,phone:o},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:i},error:o}=t;if(o)return{data:null,error:o};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:a}=Ya(i.access_token);let s=null;a.aal&&(s=a.aal);let l=s;((r=(n=i.user.factors)===null||n===void 0?void 0:n.filter(d=>d.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(l="aal2");const c=a.amr||[];return{data:{currentLevel:s,nextLevel:l,currentAuthenticationMethods:c},error:null}}))}async fetchJwk(t,n={keys:[]}){let r=n.keys.find(a=>a.kid===t);if(r||(r=this.jwks.keys.find(a=>a.kid===t),r&&this.jwks_cached_at+iw>Date.now()))return r;const{data:i,error:o}=await $(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(o)throw o;if(!i.keys||i.keys.length===0)throw new Wr("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),r=i.keys.find(a=>a.kid===t),!r)throw new Wr("No matching signing key found in JWKS");return r}async getClaims(t,n={keys:[]}){try{let r=t;if(!r){const{data:v,error:g}=await this.getSession();if(g||!v.session)return{data:null,error:g};r=v.session.access_token}const{header:i,payload:o,signature:a,raw:{header:s,payload:l}}=Ya(r);if(Pw(o.exp),!i.kid||i.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:v}=await this.getUser(r);if(v)throw v;return{data:{claims:o,header:i,signature:a},error:null}}const u=Tw(i.alg),c=await this.fetchJwk(i.kid,n),d=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,d,a,hw(`${s}.${l}`)))throw new Wr("Invalid JWT signature");return{data:{claims:o,header:i,signature:a},error:null}}catch(r){if(A(r))return{data:null,error:r};throw r}}}ci.nextInstanceID=0;const Hw=ci;class Kw extends Hw{constructor(t){super(t)}}var Jw=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};class Qw{constructor(t,n,r){var i,o,a;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const s=Y0(t),l=new URL(s);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const u=`sb-${l.hostname.split(".")[0]}-auth-token`,c={db:q0,realtime:H0,auth:Object.assign(Object.assign({},V0),{storageKey:u}),global:W0},d=Z0(r??{},c);this.storageKey=(i=d.auth.storageKey)!==null&&i!==void 0?i:"",this.headers=(o=d.global.headers)!==null&&o!==void 0?o:{},d.accessToken?(this.accessToken=d.accessToken,this.auth=new Proxy({},{get:(p,v)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(v)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((a=d.auth)!==null&&a!==void 0?a:{},this.headers,d.global.fetch),this.fetch=G0(n,this._getAccessToken.bind(this),d.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},d.realtime)),this.rest=new p0(new URL("rest/v1",l).href,{headers:this.headers,schema:d.db.schema,fetch:this.fetch}),d.accessToken||this._listenForAuthEvents()}get functions(){return new By(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new F0(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return Jw(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,storageKey:o,flowType:a,lock:s,debug:l},u,c){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Kw({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),u),storageKey:o,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,flowType:a,lock:s,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new C0(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Gw=(e,t,n)=>new Qw(e,t,n),Xw="https://jpvbtrzvbpyzgtpvltss.supabase.co",Yw="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI";Gw(Xw,Yw);class Zw{async signUp(t,n,r){try{return await(await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n,name:r})})).json()}catch{return{success:!1,error:"Network error during signup"}}}async signIn(t,n){try{const i=await(await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n})})).json();return i.success&&i.token&&localStorage.setItem("auth_token",i.token),i}catch{return{success:!1,error:"Network error during login"}}}async signOut(){try{const t=localStorage.getItem("auth_token");t&&await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${t}`}})}finally{localStorage.removeItem("auth_token")}}async getCurrentUser(){try{const t=localStorage.getItem("auth_token");if(!t)return null;const n=await fetch("/api/auth/user",{headers:{Authorization:`Bearer ${t}`}});if(!n.ok)return n.status===401&&localStorage.removeItem("auth_token"),null;const r=await n.json();return r.success?r.data:null}catch{return null}}getToken(){return localStorage.getItem("auth_token")}isAuthenticated(){return!!this.getToken()}}const Ki=new Zw,mu=Hf((e,t)=>({user:null,isLoading:!1,isAuthenticated:!1,login:async(n,r)=>{e({isLoading:!0});try{const i=await Ki.signIn(n,r);return i.success&&i.user?(e({user:i.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:i.error||"Login failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},signup:async(n,r,i)=>{e({isLoading:!0});try{const o=await Ki.signUp(n,r,i);return o.success&&o.user?(e({user:o.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:o.error||"Signup failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},logout:async()=>{e({isLoading:!0});try{await Ki.signOut()}finally{e({user:null,isAuthenticated:!1,isLoading:!1})}},checkAuth:async()=>{e({isLoading:!0});try{const n=await Ki.getCurrentUser();e({user:n,isAuthenticated:!!n,isLoading:!1})}catch{e({user:null,isAuthenticated:!1,isLoading:!1})}},updateUser:n=>{const{user:r}=t();r&&e({user:{...r,...n}})}})),Rt=({children:e,onClick:t,variant:n="primary",size:r="md",isLoading:i=!1,disabled:o=!1,type:a="button",className:s="",...l})=>{const u="font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",c={primary:"bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500",secondary:"border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500",danger:"bg-red-500 hover:bg-red-600 text-white focus:ring-red-500"},d={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"},p=`${u} ${c[n]} ${d[r]} ${s}`;return x.jsx("button",{type:a,onClick:t,disabled:o||i,className:p,...l,children:i?x.jsxs("div",{className:"flex items-center",children:[x.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[x.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),x.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):e})},cn=({label:e,placeholder:t,value:n,onChange:r,type:i="text",error:o,required:a=!1,disabled:s=!1,className:l="",...u})=>{const c=`w-full px-3 py-2 border-2 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ${o?"border-red-500":"border-gray-600 focus:border-primary-500"} ${l}`;return x.jsxs("div",{className:"w-full",children:[e&&x.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:[e,a&&x.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),x.jsx("input",{type:i,value:n,onChange:d=>r(d.target.value),placeholder:t,disabled:s,className:c,...u}),o&&x.jsx("p",{className:"mt-1 text-sm text-red-500",children:o})]})},ex=()=>{const[e,t]=k.useState(""),[n,r]=k.useState(""),[i,o]=k.useState({}),{login:a,isLoading:s}=mu(),l=ra(),u=()=>{const d={};return e?/\S+@\S+\.\S+/.test(e)||(d.email="Email is invalid"):d.email="Email is required",n||(d.password="Password is required"),o(d),Object.keys(d).length===0},c=async d=>{if(d.preventDefault(),!u())return;const p=await a(e,n);p.success?l("/dashboard"):o({general:p.error})};return x.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:x.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[x.jsxs("div",{children:[x.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Sign in to ChewyAI"}),x.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",x.jsx(Mf,{to:"/signup",className:"font-medium text-primary-500 hover:text-primary-400",children:"create a new account"})]})]}),x.jsxs("form",{className:"mt-8 space-y-6",onSubmit:c,children:[i.general&&x.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:i.general}),x.jsxs("div",{className:"space-y-4",children:[x.jsx(cn,{label:"Email address",type:"email",value:e,onChange:t,error:i.email,placeholder:"Enter your email",required:!0}),x.jsx(cn,{label:"Password",type:"password",value:n,onChange:r,error:i.password,placeholder:"Enter your password",required:!0})]}),x.jsx(Rt,{type:"submit",isLoading:s,className:"w-full",size:"lg",children:"Sign in"})]})]})})},tx=()=>{const[e,t]=k.useState({name:"",email:"",password:"",confirmPassword:""}),[n,r]=k.useState({}),{signup:i,isLoading:o}=mu(),a=ra(),s=()=>{const c={};return e.email?/\S+@\S+\.\S+/.test(e.email)||(c.email="Email is invalid"):c.email="Email is required",e.password?e.password.length<6&&(c.password="Password must be at least 6 characters"):c.password="Password is required",e.password!==e.confirmPassword&&(c.confirmPassword="Passwords do not match"),r(c),Object.keys(c).length===0},l=async c=>{if(c.preventDefault(),!s())return;const d=await i(e.email,e.password,e.name||void 0);d.success?a("/dashboard"):r({general:d.error||"Signup failed"})},u=(c,d)=>t(p=>({...p,[c]:d}));return x.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:x.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[x.jsxs("div",{children:[x.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Create your account"}),x.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",x.jsx(Mf,{to:"/login",className:"font-medium text-primary-500 hover:text-primary-400",children:"sign in to your existing account"})]})]}),x.jsxs("form",{className:"mt-8 space-y-6",onSubmit:l,children:[n.general&&x.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:n.general}),x.jsxs("div",{className:"space-y-4",children:[x.jsx(cn,{label:"Full Name (Optional)",value:e.name,onChange:c=>u("name",c),placeholder:"Enter your full name"}),x.jsx(cn,{label:"Email address",type:"email",value:e.email,onChange:c=>u("email",c),error:n.email,placeholder:"Enter your email",required:!0}),x.jsx(cn,{label:"Password",type:"password",value:e.password,onChange:c=>u("password",c),error:n.password,placeholder:"Create a password",required:!0}),x.jsx(cn,{label:"Confirm Password",type:"password",value:e.confirmPassword,onChange:c=>u("confirmPassword",c),error:n.confirmPassword,placeholder:"Confirm your password",required:!0})]}),x.jsx(Rt,{type:"submit",isLoading:o,className:"w-full",size:"lg",children:"Create Account"})]})]})})},ad=({children:e})=>{const{isAuthenticated:t,isLoading:n,checkAuth:r}=mu(),i=fr();return k.useEffect(()=>{r()},[r]),n?x.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:x.jsx("div",{className:"text-white",children:"Loading..."})}):t?x.jsx(x.Fragment,{children:e}):x.jsx(Ff,{to:"/login",state:{from:i},replace:!0})},nx=()=>x.jsx("div",{className:"min-h-screen bg-background-primary text-white flex items-center justify-center",children:x.jsx("h1",{className:"text-3xl font-bold",children:"Welcome to ChewyAI Dashboard"})});var gh={exports:{}},rx="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",ix=rx,ox=ix;function yh(){}function wh(){}wh.resetWarningCache=yh;var ax=function(){function e(r,i,o,a,s,l){if(l!==ox){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:wh,resetWarningCache:yh};return n.PropTypes=n,n};gh.exports=ax();var sx=gh.exports;const M=dl(sx);function _n(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})}const lx=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function sr(e,t,n){const r=ux(e),{webkitRelativePath:i}=e,o=typeof t=="string"?t:typeof i=="string"&&i.length>0?i:`./${e.name}`;return typeof r.path!="string"&&sd(r,"path",o),n!==void 0&&Object.defineProperty(r,"handle",{value:n,writable:!1,configurable:!1,enumerable:!0}),sd(r,"relativePath",o),r}function ux(e){const{name:t}=e;if(t&&t.lastIndexOf(".")!==-1&&!e.type){const r=t.split(".").pop().toLowerCase(),i=lx.get(r);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}function sd(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}const cx=[".DS_Store","Thumbs.db"];function dx(e){return _n(this,void 0,void 0,function*(){return $o(e)&&px(e.dataTransfer)?vx(e.dataTransfer,e.type):fx(e)?hx(e):Array.isArray(e)&&e.every(t=>"getFile"in t&&typeof t.getFile=="function")?mx(e):[]})}function px(e){return $o(e)}function fx(e){return $o(e)&&$o(e.target)}function $o(e){return typeof e=="object"&&e!==null}function hx(e){return al(e.target.files).map(t=>sr(t))}function mx(e){return _n(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>n.getFile()))).map(n=>sr(n))})}function vx(e,t){return _n(this,void 0,void 0,function*(){if(e.items){const n=al(e.items).filter(i=>i.kind==="file");if(t!=="drop")return n;const r=yield Promise.all(n.map(gx));return ld(xh(r))}return ld(al(e.files).map(n=>sr(n)))})}function ld(e){return e.filter(t=>cx.indexOf(t.name)===-1)}function al(e){if(e===null)return[];const t=[];for(let n=0;n<e.length;n++){const r=e[n];t.push(r)}return t}function gx(e){if(typeof e.webkitGetAsEntry!="function")return ud(e);const t=e.webkitGetAsEntry();return t&&t.isDirectory?_h(t):ud(e,t)}function xh(e){return e.reduce((t,n)=>[...t,...Array.isArray(n)?xh(n):[n]],[])}function ud(e,t){return _n(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const o=yield e.getAsFileSystemHandle();if(o===null)throw new Error(`${e} is not a File`);if(o!==void 0){const a=yield o.getFile();return a.handle=o,sr(a)}}const r=e.getAsFile();if(!r)throw new Error(`${e} is not a File`);return sr(r,(n=t==null?void 0:t.fullPath)!==null&&n!==void 0?n:void 0)})}function yx(e){return _n(this,void 0,void 0,function*(){return e.isDirectory?_h(e):wx(e)})}function _h(e){const t=e.createReader();return new Promise((n,r)=>{const i=[];function o(){t.readEntries(a=>_n(this,void 0,void 0,function*(){if(a.length){const s=Promise.all(a.map(yx));i.push(s),o()}else try{const s=yield Promise.all(i);n(s)}catch(s){r(s)}}),a=>{r(a)})}o()})}function wx(e){return _n(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(r=>{const i=sr(r,e.fullPath);t(i)},r=>{n(r)})})})}var es=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(n.length===0)return!0;var r=e.name||"",i=(e.type||"").toLowerCase(),o=i.replace(/\/.*$/,"");return n.some(function(a){var s=a.trim().toLowerCase();return s.charAt(0)==="."?r.toLowerCase().endsWith(s):s.endsWith("/*")?o===s.replace(/\/.*$/,""):i===s})}return!0};function cd(e){return kx(e)||_x(e)||Sh(e)||xx()}function xx(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _x(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function kx(e){if(Array.isArray(e))return sl(e)}function dd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function pd(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?dd(Object(n),!0).forEach(function(r){kh(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):dd(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function kh(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function di(e,t){return bx(e)||Ex(e,t)||Sh(e,t)||Sx()}function Sx(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Sh(e,t){if(e){if(typeof e=="string")return sl(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return sl(e,t)}}function sl(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ex(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],i=!0,o=!1,a,s;try{for(n=n.call(e);!(i=(a=n.next()).done)&&(r.push(a.value),!(t&&r.length===t));i=!0);}catch(l){o=!0,s=l}finally{try{!i&&n.return!=null&&n.return()}finally{if(o)throw s}}return r}}function bx(e){if(Array.isArray(e))return e}var jx=typeof es=="function"?es:es.default,Cx="file-invalid-type",Px="file-too-large",Tx="file-too-small",Ox="too-many-files",Rx=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=t.split(","),r=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:Cx,message:"File type must be ".concat(r)}},fd=function(t){return{code:Px,message:"File is larger than ".concat(t," ").concat(t===1?"byte":"bytes")}},hd=function(t){return{code:Tx,message:"File is smaller than ".concat(t," ").concat(t===1?"byte":"bytes")}},Ix={code:Ox,message:"Too many files"};function Eh(e,t){var n=e.type==="application/x-moz-file"||jx(e,t);return[n,n?null:Rx(t)]}function bh(e,t,n){if(an(e.size))if(an(t)&&an(n)){if(e.size>n)return[!1,fd(n)];if(e.size<t)return[!1,hd(t)]}else{if(an(t)&&e.size<t)return[!1,hd(t)];if(an(n)&&e.size>n)return[!1,fd(n)]}return[!0,null]}function an(e){return e!=null}function Ax(e){var t=e.files,n=e.accept,r=e.minSize,i=e.maxSize,o=e.multiple,a=e.maxFiles,s=e.validator;return!o&&t.length>1||o&&a>=1&&t.length>a?!1:t.every(function(l){var u=Eh(l,n),c=di(u,1),d=c[0],p=bh(l,r,i),v=di(p,1),g=v[0],w=s?s(l):null;return d&&g&&!w})}function No(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function Ji(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(t){return t==="Files"||t==="application/x-moz-file"}):!!e.target&&!!e.target.files}function md(e){e.preventDefault()}function Dx(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function Lx(e){return e.indexOf("Edge/")!==-1}function $x(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return Dx(e)||Lx(e)}function ot(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){for(var i=arguments.length,o=new Array(i>1?i-1:0),a=1;a<i;a++)o[a-1]=arguments[a];return t.some(function(s){return!No(r)&&s&&s.apply(void 0,[r].concat(o)),No(r)})}}function Nx(){return"showOpenFilePicker"in window}function zx(e){if(an(e)){var t=Object.entries(e).filter(function(n){var r=di(n,2),i=r[0],o=r[1],a=!0;return jh(i)||(console.warn('Skipped "'.concat(i,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),a=!1),(!Array.isArray(o)||!o.every(Ch))&&(console.warn('Skipped "'.concat(i,'" because an invalid file extension was provided.')),a=!1),a}).reduce(function(n,r){var i=di(r,2),o=i[0],a=i[1];return pd(pd({},n),{},kh({},o,a))},{});return[{description:"Files",accept:t}]}return e}function Ux(e){if(an(e))return Object.entries(e).reduce(function(t,n){var r=di(n,2),i=r[0],o=r[1];return[].concat(cd(t),[i],cd(o))},[]).filter(function(t){return jh(t)||Ch(t)}).join(",")}function Fx(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function Mx(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function jh(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function Ch(e){return/^.*\.[\w]+$/.test(e)}var Bx=["children"],Wx=["open"],qx=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Vx=["refKey","onChange","onClick"];function Hx(e){return Qx(e)||Jx(e)||Ph(e)||Kx()}function Kx(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Jx(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Qx(e){if(Array.isArray(e))return ll(e)}function ts(e,t){return Yx(e)||Xx(e,t)||Ph(e,t)||Gx()}function Gx(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ph(e,t){if(e){if(typeof e=="string")return ll(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ll(e,t)}}function ll(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Xx(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],i=!0,o=!1,a,s;try{for(n=n.call(e);!(i=(a=n.next()).done)&&(r.push(a.value),!(t&&r.length===t));i=!0);}catch(l){o=!0,s=l}finally{try{!i&&n.return!=null&&n.return()}finally{if(o)throw s}}return r}}function Yx(e){if(Array.isArray(e))return e}function vd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Q(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?vd(Object(n),!0).forEach(function(r){ul(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):vd(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function ul(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function zo(e,t){if(e==null)return{};var n=Zx(e,t),r,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)r=o[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Zx(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}var vu=k.forwardRef(function(e,t){var n=e.children,r=zo(e,Bx),i=Oh(r),o=i.open,a=zo(i,Wx);return k.useImperativeHandle(t,function(){return{open:o}},[o]),Fo.createElement(k.Fragment,null,n(Q(Q({},a),{},{open:o})))});vu.displayName="Dropzone";var Th={disabled:!1,getFilesFromEvent:dx,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};vu.defaultProps=Th;vu.propTypes={children:M.func,accept:M.objectOf(M.arrayOf(M.string)),multiple:M.bool,preventDropOnDocument:M.bool,noClick:M.bool,noKeyboard:M.bool,noDrag:M.bool,noDragEventsBubbling:M.bool,minSize:M.number,maxSize:M.number,maxFiles:M.number,disabled:M.bool,getFilesFromEvent:M.func,onFileDialogCancel:M.func,onFileDialogOpen:M.func,useFsAccessApi:M.bool,autoFocus:M.bool,onDragEnter:M.func,onDragLeave:M.func,onDragOver:M.func,onDrop:M.func,onDropAccepted:M.func,onDropRejected:M.func,onError:M.func,validator:M.func};var cl={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function Oh(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=Q(Q({},Th),e),n=t.accept,r=t.disabled,i=t.getFilesFromEvent,o=t.maxSize,a=t.minSize,s=t.multiple,l=t.maxFiles,u=t.onDragEnter,c=t.onDragLeave,d=t.onDragOver,p=t.onDrop,v=t.onDropAccepted,g=t.onDropRejected,w=t.onFileDialogCancel,_=t.onFileDialogOpen,m=t.useFsAccessApi,h=t.autoFocus,f=t.preventDropOnDocument,y=t.noClick,S=t.noKeyboard,b=t.noDrag,j=t.noDragEventsBubbling,O=t.onError,F=t.validator,D=k.useMemo(function(){return Ux(n)},[n]),Ee=k.useMemo(function(){return zx(n)},[n]),ct=k.useMemo(function(){return typeof _=="function"?_:gd},[_]),rt=k.useMemo(function(){return typeof w=="function"?w:gd},[w]),ae=k.useRef(null),be=k.useRef(null),vr=k.useReducer(e_,cl),kn=ts(vr,2),P=kn[0],R=kn[1],L=P.isFocused,H=P.isFileDialogActive,J=k.useRef(typeof window<"u"&&window.isSecureContext&&m&&Nx()),Yt=function(){!J.current&&H&&setTimeout(function(){if(be.current){var I=be.current.files;I.length||(R({type:"closeDialog"}),rt())}},300)};k.useEffect(function(){return window.addEventListener("focus",Yt,!1),function(){window.removeEventListener("focus",Yt,!1)}},[be,H,rt,J]);var je=k.useRef([]),Sn=function(I){ae.current&&ae.current.contains(I.target)||(I.preventDefault(),je.current=[])};k.useEffect(function(){return f&&(document.addEventListener("dragover",md,!1),document.addEventListener("drop",Sn,!1)),function(){f&&(document.removeEventListener("dragover",md),document.removeEventListener("drop",Sn))}},[ae,f]),k.useEffect(function(){return!r&&h&&ae.current&&ae.current.focus(),function(){}},[ae,h,r]);var we=k.useCallback(function(C){O?O(C):console.error(C)},[O]),Zt=k.useCallback(function(C){C.preventDefault(),C.persist(),Si(C),je.current=[].concat(Hx(je.current),[C.target]),Ji(C)&&Promise.resolve(i(C)).then(function(I){if(!(No(C)&&!j)){var ne=I.length,se=ne>0&&Ax({files:I,accept:D,minSize:a,maxSize:o,multiple:s,maxFiles:l,validator:F}),De=ne>0&&!se;R({isDragAccept:se,isDragReject:De,isDragActive:!0,type:"setDraggedFiles"}),u&&u(C)}}).catch(function(I){return we(I)})},[i,u,we,j,D,a,o,s,l,F]),yu=k.useCallback(function(C){C.preventDefault(),C.persist(),Si(C);var I=Ji(C);if(I&&C.dataTransfer)try{C.dataTransfer.dropEffect="copy"}catch{}return I&&d&&d(C),!1},[d,j]),wu=k.useCallback(function(C){C.preventDefault(),C.persist(),Si(C);var I=je.current.filter(function(se){return ae.current&&ae.current.contains(se)}),ne=I.indexOf(C.target);ne!==-1&&I.splice(ne,1),je.current=I,!(I.length>0)&&(R({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Ji(C)&&c&&c(C))},[ae,c,j]),xi=k.useCallback(function(C,I){var ne=[],se=[];C.forEach(function(De){var gr=Eh(De,D),jn=ts(gr,2),fa=jn[0],ha=jn[1],ma=bh(De,a,o),Ei=ts(ma,2),va=Ei[0],ga=Ei[1],ya=F?F(De):null;if(fa&&va&&!ya)ne.push(De);else{var wa=[ha,ga];ya&&(wa=wa.concat(ya)),se.push({file:De,errors:wa.filter(function(Dh){return Dh})})}}),(!s&&ne.length>1||s&&l>=1&&ne.length>l)&&(ne.forEach(function(De){se.push({file:De,errors:[Ix]})}),ne.splice(0)),R({acceptedFiles:ne,fileRejections:se,isDragReject:se.length>0,type:"setFiles"}),p&&p(ne,se,I),se.length>0&&g&&g(se,I),ne.length>0&&v&&v(ne,I)},[R,s,D,a,o,l,p,v,g,F]),_i=k.useCallback(function(C){C.preventDefault(),C.persist(),Si(C),je.current=[],Ji(C)&&Promise.resolve(i(C)).then(function(I){No(C)&&!j||xi(I,C)}).catch(function(I){return we(I)}),R({type:"reset"})},[i,xi,we,j]),En=k.useCallback(function(){if(J.current){R({type:"openDialog"}),ct();var C={multiple:s,types:Ee};window.showOpenFilePicker(C).then(function(I){return i(I)}).then(function(I){xi(I,null),R({type:"closeDialog"})}).catch(function(I){Fx(I)?(rt(I),R({type:"closeDialog"})):Mx(I)?(J.current=!1,be.current?(be.current.value=null,be.current.click()):we(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):we(I)});return}be.current&&(R({type:"openDialog"}),ct(),be.current.value=null,be.current.click())},[R,ct,rt,m,xi,we,Ee,s]),xu=k.useCallback(function(C){!ae.current||!ae.current.isEqualNode(C.target)||(C.key===" "||C.key==="Enter"||C.keyCode===32||C.keyCode===13)&&(C.preventDefault(),En())},[ae,En]),_u=k.useCallback(function(){R({type:"focus"})},[]),ku=k.useCallback(function(){R({type:"blur"})},[]),Su=k.useCallback(function(){y||($x()?setTimeout(En,0):En())},[y,En]),bn=function(I){return r?null:I},pa=function(I){return S?null:bn(I)},ki=function(I){return b?null:bn(I)},Si=function(I){j&&I.stopPropagation()},Rh=k.useMemo(function(){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},I=C.refKey,ne=I===void 0?"ref":I,se=C.role,De=C.onKeyDown,gr=C.onFocus,jn=C.onBlur,fa=C.onClick,ha=C.onDragEnter,ma=C.onDragOver,Ei=C.onDragLeave,va=C.onDrop,ga=zo(C,qx);return Q(Q(ul({onKeyDown:pa(ot(De,xu)),onFocus:pa(ot(gr,_u)),onBlur:pa(ot(jn,ku)),onClick:bn(ot(fa,Su)),onDragEnter:ki(ot(ha,Zt)),onDragOver:ki(ot(ma,yu)),onDragLeave:ki(ot(Ei,wu)),onDrop:ki(ot(va,_i)),role:typeof se=="string"&&se!==""?se:"presentation"},ne,ae),!r&&!S?{tabIndex:0}:{}),ga)}},[ae,xu,_u,ku,Su,Zt,yu,wu,_i,S,b,r]),Ih=k.useCallback(function(C){C.stopPropagation()},[]),Ah=k.useMemo(function(){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},I=C.refKey,ne=I===void 0?"ref":I,se=C.onChange,De=C.onClick,gr=zo(C,Vx),jn=ul({accept:D,multiple:s,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:bn(ot(se,_i)),onClick:bn(ot(De,Ih)),tabIndex:-1},ne,be);return Q(Q({},jn),gr)}},[be,n,s,_i,r]);return Q(Q({},P),{},{isFocused:L&&!r,getRootProps:Rh,getInputProps:Ah,rootRef:ae,inputRef:be,open:bn(En)})}function e_(e,t){switch(t.type){case"focus":return Q(Q({},e),{},{isFocused:!0});case"blur":return Q(Q({},e),{},{isFocused:!1});case"openDialog":return Q(Q({},cl),{},{isFileDialogActive:!0});case"closeDialog":return Q(Q({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return Q(Q({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return Q(Q({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return Q({},cl);default:return e}}function gd(){}const gu=Hf(e=>({documents:[],selectedDocuments:new Set,isLoading:!1,uploadProgress:{},fetchDocuments:async()=>{e({isLoading:!0});try{const t=localStorage.getItem("auth_token"),n=await fetch("/api/documents",{headers:{Authorization:`Bearer ${t}`}});if(!n.ok)throw new Error("Failed to fetch documents");const r=await n.json();if(r.success)e({documents:r.data,isLoading:!1});else throw new Error(r.error)}catch(t){throw console.error("Fetch documents error:",t),e({isLoading:!1}),t}},uploadDocument:async t=>{const n=new FormData;n.append("document",t);try{const r=localStorage.getItem("auth_token"),i=await fetch("/api/documents/upload",{method:"POST",headers:{Authorization:`Bearer ${r}`},body:n});if(!i.ok){const a=await i.json();throw new Error(a.error||"Upload failed")}const o=await i.json();if(o.success)return e(a=>({documents:[o.data,...a.documents],uploadProgress:{...a.uploadProgress,[t.name]:100}})),o.data;throw new Error(o.error)}catch(r){throw console.error("Upload document error:",r),r}},deleteDocument:async t=>{try{const n=localStorage.getItem("auth_token"),r=await fetch(`/api/documents/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${n}`}});if(!r.ok){const i=await r.json();throw new Error(i.error||"Delete failed")}e(i=>({documents:i.documents.filter(o=>o.id!==t),selectedDocuments:new Set([...i.selectedDocuments].filter(o=>o!==t))}))}catch(n){throw console.error("Delete document error:",n),n}},searchDocuments:async t=>{try{const n=localStorage.getItem("auth_token"),r=await fetch(`/api/documents/search?q=${encodeURIComponent(t)}`,{headers:{Authorization:`Bearer ${n}`}});if(!r.ok)throw new Error("Search failed");const i=await r.json();return i.success?i.data:[]}catch(n){return console.error("Search documents error:",n),[]}},getDocument:async t=>{try{const n=localStorage.getItem("auth_token"),r=await fetch(`/api/documents/${t}`,{headers:{Authorization:`Bearer ${n}`}});if(!r.ok)return null;const i=await r.json();return i.success?i.data:null}catch(n){return console.error("Get document error:",n),null}},toggleDocumentSelection:t=>{e(n=>{const r=new Set(n.selectedDocuments);return r.has(t)?r.delete(t):r.add(t),{selectedDocuments:r}})},clearSelection:()=>{e({selectedDocuments:new Set})},selectAll:()=>{e(t=>({selectedDocuments:new Set(t.documents.map(n=>n.id))}))},setUploadProgress:(t,n)=>{e(r=>({uploadProgress:{...r.uploadProgress,[t]:n}}))}})),t_=()=>{const[e,t]=k.useState(!1),[n,r]=k.useState([]),{uploadDocument:i,setUploadProgress:o}=gu(),a=k.useCallback(async c=>{t(!0),r([]);const d=[];for(const p of c)try{if(p.size>10*1024*1024){d.push(`${p.name}: File size exceeds 10MB limit`);continue}if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","application/vnd.openxmlformats-officedocument.presentationml.presentation"].includes(p.type)){d.push(`${p.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);continue}o(p.name,0),await i(p),o(p.name,100)}catch(v){d.push(`${p.name}: ${v instanceof Error?v.message:"Unknown error"}`)}r(d),t(!1)},[i,o]),{getRootProps:s,getInputProps:l,isDragActive:u}=Oh({onDrop:a,accept:{"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"]},multiple:!0,disabled:e});return x.jsxs("div",{className:"space-y-4",children:[x.jsxs("div",{...s(),className:`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${u?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-primary-500 hover:bg-primary-500/5"}
          ${e?"opacity-50 cursor-not-allowed":""}
        `,children:[x.jsx("input",{...l()}),x.jsxs("div",{className:"space-y-2",children:[x.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:x.jsx("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),u?x.jsx("p",{className:"text-primary-400",children:"Drop the files here..."}):x.jsxs("div",{children:[x.jsxs("p",{className:"text-gray-300",children:["Drag & drop files here, or"," ",x.jsx("span",{className:"text-primary-500 font-medium",children:"browse"})]}),x.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Supports PDF, DOCX, TXT, PPTX (max 10MB each)"})]})]})]}),e&&x.jsxs("div",{className:"bg-background-secondary rounded-lg p-4",children:[x.jsx("p",{className:"text-sm text-gray-300 mb-2",children:"Uploading files..."}),x.jsx("div",{className:"space-y-2"})]}),n.length>0&&x.jsxs("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4",children:[x.jsx("h4",{className:"text-red-400 font-medium mb-2",children:"Upload Errors:"}),x.jsx("ul",{className:"text-sm text-red-300 space-y-1",children:n.map((c,d)=>x.jsxs("li",{children:["• ",c]},d))})]})]})},n_=({document:e})=>{const{selectedDocuments:t,toggleDocumentSelection:n,deleteDocument:r}=gu(),[i,o]=k.useState(!1),a=t.has(e.id),s=async()=>{if(window.confirm(`Are you sure you want to delete "${e.filename}"? This action cannot be undone.`)){o(!0);try{await r(e.id)}catch(p){console.error("Delete error:",p),alert("Failed to delete document. Please try again.")}finally{o(!1)}}},l=d=>{if(d===0)return"0 Bytes";const p=1024,v=["Bytes","KB","MB","GB"],g=Math.floor(Math.log(d)/Math.log(p));return parseFloat((d/Math.pow(p,g)).toFixed(2))+" "+v[g]},u=d=>new Date(d).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),c=d=>({pdf:"📄",docx:"📝",txt:"📃",pptx:"📊"})[d]||"📄";return x.jsxs("div",{className:`
        bg-background-secondary rounded-lg p-4 border-2 transition-all cursor-pointer
        ${a?"border-primary-500 bg-primary-500/10":"border-gray-700 hover:border-gray-600"}
      `,onClick:()=>n(e.id),children:[x.jsxs("div",{className:"flex items-start justify-between mb-3",children:[x.jsxs("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[x.jsx("span",{className:"text-2xl",children:c(e.file_type)}),x.jsxs("div",{className:"min-w-0 flex-1",children:[x.jsx("h3",{className:"text-white font-medium truncate",title:e.filename,children:e.filename}),x.jsxs("p",{className:"text-sm text-gray-400",children:[e.file_type.toUpperCase()," • ",l(e.file_size)]})]})]}),x.jsx("div",{className:`
            w-5 h-5 rounded border-2 flex items-center justify-center
            ${a?"bg-primary-500 border-primary-500":"border-gray-500"}
          `,children:a&&x.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:x.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),!e.is_processed&&x.jsx("div",{className:"mb-3",children:x.jsxs("div",{className:"flex items-center space-x-2 text-yellow-400",children:[x.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"}),x.jsx("span",{className:"text-sm",children:"Processing..."})]})}),e.processing_error&&x.jsx("div",{className:"mb-3",children:x.jsxs("div",{className:"text-red-400 text-sm",children:["⚠️ Processing failed: ",e.processing_error]})}),x.jsxs("div",{className:"text-xs text-gray-500 mb-3",children:["Uploaded ",u(e.uploaded_at)]}),x.jsx("div",{className:"flex justify-end space-x-2",onClick:d=>d.stopPropagation(),children:x.jsx(Rt,{onClick:s,variant:"danger",size:"sm",isLoading:i,children:"Delete"})})]})},r_=()=>{const{documents:e,selectedDocuments:t,isLoading:n,fetchDocuments:r,searchDocuments:i,clearSelection:o,selectAll:a,deleteDocument:s}=gu(),[l,u]=k.useState(""),[c,d]=k.useState(null),[p,v]=k.useState(!1);k.useEffect(()=>{r()},[r]);const g=async()=>{if(l.trim().length<2){d(null);return}v(!0);try{const f=await i(l.trim());d(f)}catch(f){console.error("Search error:",f)}finally{v(!1)}},w=()=>{u(""),d(null)},_=async()=>{if(!(t.size===0||!window.confirm(`Are you sure you want to delete ${t.size} document(s)? This action cannot be undone.`)))try{const y=Array.from(t).map(S=>s(S));await Promise.all(y),o()}catch(y){console.error("Bulk delete error:",y),alert("Some documents could not be deleted. Please try again.")}},m=c||e,h=t.size>0;return n&&e.length===0?x.jsx("div",{className:"flex items-center justify-center py-12",children:x.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):x.jsxs("div",{className:"space-y-6",children:[x.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[x.jsx("div",{className:"flex-1",children:x.jsxs("div",{className:"flex gap-2",children:[x.jsx(cn,{placeholder:"Search documents...",value:l,onChange:u}),x.jsx(Rt,{onClick:g,isLoading:p,disabled:l.trim().length<2,children:"Search"}),c&&x.jsx(Rt,{onClick:w,variant:"secondary",children:"Clear"})]})}),h&&x.jsxs("div",{className:"flex gap-2",children:[x.jsx(Rt,{onClick:a,variant:"secondary",size:"sm",children:"Select All"}),x.jsxs(Rt,{onClick:o,variant:"secondary",size:"sm",children:["Clear (",t.size,")"]}),x.jsx(Rt,{onClick:_,variant:"danger",size:"sm",children:"Delete Selected"})]})]}),c&&x.jsxs("div",{className:"text-sm text-gray-400",children:["Found ",c.length,' document(s) matching "',l,'"']}),m.length===0?x.jsxs("div",{className:"text-center py-12",children:[x.jsx("div",{className:"text-gray-400 mb-4",children:c?"No documents found matching your search.":"No documents uploaded yet."}),!c&&x.jsx("p",{className:"text-sm text-gray-500",children:"Upload your first document to get started with AI-powered study materials."})]}):x.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:m.map(f=>x.jsx(n_,{document:f},f.id))})]})},i_=()=>x.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:x.jsxs("div",{className:"space-y-8",children:[x.jsxs("div",{children:[x.jsx("h1",{className:"text-3xl font-bold text-white",children:"Documents"}),x.jsx("p",{className:"mt-2 text-gray-400",children:"Upload and manage your study documents. Supported formats: PDF, DOCX, TXT, PPTX"})]}),x.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[x.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Upload Documents"}),x.jsx(t_,{})]}),x.jsxs("div",{children:[x.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Documents"}),x.jsx(r_,{})]})]})});function o_(){return x.jsx(ay,{children:x.jsxs(Zg,{children:[x.jsx(In,{path:"/",element:x.jsx(Ff,{to:"/login"})}),x.jsx(In,{path:"/login",element:x.jsx(ex,{})}),x.jsx(In,{path:"/signup",element:x.jsx(tx,{})}),x.jsx(In,{path:"/dashboard",element:x.jsx(ad,{children:x.jsx(nx,{})})}),x.jsx(In,{path:"/documents",element:x.jsx(ad,{children:x.jsx(i_,{})})})]})})}ns.createRoot(document.getElementById("root")).render(x.jsx(Fo.StrictMode,{children:x.jsx(o_,{})}));
